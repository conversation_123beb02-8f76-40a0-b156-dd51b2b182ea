import json

import requests

mcp_url = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{}"

response = requests.get(
    "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps?page=1&page_size=10"
)
total_pages = response.json()["metadata"]["total_pages"]

final_output = {}

for page in range(1, total_pages + 1):
    response = requests.get(
        f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps?page={page}&page_size=10"
    )
    mcps = response.json()["data"]
    for mcp in mcps:
        mcp_id = mcp["id"]
        response = requests.get(mcp_url.format(mcp_id))
        mcp_data = response.json()["mcp"]
        mcp_type = mcp_data["mcp_type"]
        if mcp_type not in final_output:
            final_output[mcp_type] = [{"mcp_id": mcp_id, "mcp_name": mcp_data["name"]}]
        else:
            final_output[mcp_type].append(
                {"mcp_id": mcp_id, "mcp_name": mcp_data["name"]}
            )

with open("filter_mcp_type.json", "w") as f:
    json.dump(final_output, f, indent=2)
