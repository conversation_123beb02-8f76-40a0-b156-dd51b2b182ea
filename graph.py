from strands.multiagent import <PERSON>raph<PERSON>uild<PERSON>


def get_graph(agents):
    prompt_enhancement_agent = agents["prompt_enhancement"]
    post_processing_agent = agents["post_processing"]
    main_agent = agents["main"]

    builder = GraphBuilder()

    builder.add_node(prompt_enhancement_agent, "prompt_enhancement")
    builder.add_node(post_processing_agent, "post_processing")
    builder.add_node(main_agent, "main")

    builder.add_edge("prompt_enhancement", "main")
    builder.add_edge("main", "post_processing")

    builder.set_entry_point("prompt_enhancement")

    return builder.build()
