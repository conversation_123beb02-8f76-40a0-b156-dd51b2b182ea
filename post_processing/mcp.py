import json
from datetime import datetime
from typing import Any, Dict

import requests


def fulfill_mcp(
    node_info: dict,
    api_detail_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/",
) -> Dict[str, Any]:
    """
    Fetches an MCP from the marketplace by ID and constructs a WorkflowNode.
    Handles the detail API structure (object under 'mcp').
    No authentication required.
    """
    mcp_id = node_info["mcp_id"]
    tool_name = node_info["tool_name"]
    node_position = node_info["position"]
    node_config = node_info.get("parameters", {})
    api_detail_url = api_detail_url + mcp_id
    # 1) Fetch MCP detail
    resp = requests.get(
        api_detail_url.format(mcp_id=mcp_id),
        headers={"Content-Type": "application/json"},
    )
    resp.raise_for_status()
    data = resp.json()
    mcp = data.get("mcp") or data.get("data") or data  # <-- flexible extraction
    # with open(f"post_processing/data retrival/mcps/{mcp_id}.json", "r") as f:
    #     data = json.load(f)
    # mcp = data.get("mcp") or data.get("data") or data   # <-- flexible extraction
    if not mcp:
        raise ValueError(f"No MCP with id={mcp_id} found in marketplace API")

    # 2) Defaults
    if node_config is None:
        node_config = {}

    # 3) Inputs from tools (since input_schema is inside tools, not top-level)
    inputs = []
    required = []
    props = {}

    tools = mcp.get("mcp_tools_config", {}).get("tools", [])
    if tools:
        tool = next(tool for tool in tools if tool["name"] == tool_name)
        props = tool.get("input_schema", {}).get("properties", {})
        required = set(tool.get("input_schema", {}).get("required", []))
        if tool.get("output_schema"):
            has_output = True
            outputs_schema = tool.get("output_schema", {}).get("properties", {})
        else:
            has_output = False
            outputs_schema = {}

    for name, schema in props.items():
        input_type = schema.get("type")
        itype = (
            "array"
            if input_type == "array" or "items" in schema
            else input_type or "string"
        )
        inputs.append(
            {
                "name": name,
                "display_name": name[0].upper() + name[1:].replace("_", " "),
                "info": "",
                "input_type": itype,
                "input_types": [itype, "Any"],
                "required": name in required,
                "is_handle": True,
                "is_list": itype == "array",
                "real_time_refresh": False,
                "advanced": False,
                "value": None,
                "options": None,
                "visibility_rules": None,
                "visibility_logic": "OR",
                "validation": {},
            }
        )

    # 4) Outputs
    outputs = []
    if has_output:
        for name, schema in outputs_schema.items():
            outputs.append(
                {
                    "name": name,
                    "display_name": schema.get("title", name),
                    "output_type": schema.get("type", "Any"),
                }
            )
    else:
        outputs.append(
            {"name": "result", "display_name": "Result", "output_type": "Any"}
        )

    # 5) Definition
    definition = {
        "name": mcp_id,
        "display_name": mcp.get("name", mcp_id),
        "description": mcp.get("description", ""),
        "category": mcp.get("category", ""),
        "icon": (mcp.get("logo") or "").split("/")[-1].split(".")[0].capitalize(),
        "beta": False,
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        # "path": mcp.get("hosted_url", ""),
        "type": "MCP",
        # "env_keys": [],
        # "env_credential_status": "",
        "logo": mcp.get("logo", ""),
        # "oauth_details": {},
        "mcp_info": {
            "server_id": mcp_id,
            "server_path": "",
            "tool_name": tool_name,
            "input_schema": tool.get("input_schema", {}),
            "output_schema": tool.get("output_schema", {}),
        },
    }

    # 6) WorkflowNode
    node = {
        "id": node_info["node_id"],
        "type": "WorkflowNode",
        "position": node_position,
        "data": {
            "label": node_info["label"],
            "type": "mcp",
            "originalType": node_info["OriginalType"],
            "definition": definition,
            "config": {},
            "oauthConnectionState": {},
        },
        "width": 388,
        "height": 519,
        "selected": True,
        "positionAbsolute": node_position,
        "dragging": False,
        "style": {"opacity": 1},
    }
    node["position"] = node_info["position"]
    node["data"]["label"] = node_info["label"]
    node["data"]["config"] = node_info.get("parameters", {})
    node["width"] = node_info["dimension"]["width"]
    node["height"] = node_info["dimension"]["height"]
    if mcp.get("integrations"):
        node["data"]["definition"]["integrations"] = mcp["integrations"]
    inputs = node["data"]["definition"]["inputs"]
    for input_ in inputs:
        if input_["value"] and input_["name"] not in node_info.get("parameters", {}):
            node["data"]["config"][input_["name"]] = input_["value"]
    return node


# if __name__ == "__main__":
#     mcp_id = "37db65ab-0586-434e-a58d-7ddc6d9a8beb"
#     tool_name = "send_email"
#     node = fulfill_mcp(mcp_id, tool_name)

#     filename = f"{mcp_id}_node.json"
#     with open(filename, "w", encoding="utf-8") as f:
#         json.dump(node, f, indent=2)

#     print(f"✅ Node saved to {filename}")
