import json
from datetime import datetime
from typing import Any, Dict

import requests


def fulfill_workflow(
    node_info: dict,
    api_base_url: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/",
) -> Dict[str, Any]:
    workflow_id = node_info["workflow_id"]
    label = node_info["label"]
    node_position = node_info["position"]
    node_config = node_info.get("parameters", {})
    # Construct the API URL using marketplace endpoint
    api_url = f"{api_base_url}{workflow_id}"

    # Simple headers - no authentication required for marketplace
    headers = {"Content-Type": "application/json"}
    try:
        # Make the API request
        response = requests.get(api_url, headers=headers)

        # Handle different error cases
        if response.status_code == 404:
            raise Exception(f"❌ Workflow not found in marketplace: {workflow_id}")
        elif response.status_code == 500:
            raise Exception("❌ Internal server error - try again later")
        elif response.status_code != 200:
            raise Exception(f"❌ API returned status code: {response.status_code}")

        response.raise_for_status()

        # Parse the JSON response
        api_response = response.json()
        # with open(f"post_processing/data retrival/workflows/{workflow_id}.json", "r") as f:
        #     api_response = json.load(f)
        # Check if it's a successful response
        if not api_response:
            raise ValueError("Empty response from marketplace API")

        # Handle different possible response formats from marketplace
        workflow = None
        if isinstance(api_response, dict):
            # Check for common response wrapper formats
            if "workflow" in api_response:
                workflow = api_response["workflow"]
            elif "data" in api_response:
                workflow = api_response["data"]
            elif "result" in api_response:
                workflow = api_response["result"]
            else:
                # Assume the response itself is the workflow data
                workflow = api_response
        else:
            raise ValueError("Unexpected response format from marketplace API")

        if not workflow:
            raise ValueError("No workflow data found in response")

        # Generate unique node ID
        node_id = node_info["node_id"]

        # Extract start nodes and create inputs
        inputs = []
        start_nodes = workflow.get("start_nodes", [])

        for start_node in start_nodes:
            is_handle_value = start_node.get("type") == "handle"

            input_item = {
                "name": start_node.get("field", "input_data"),
                "display_name": start_node.get("field", "Input Data")
                .replace("_", " ")
                .title(),
                "info": f"Input field: {start_node.get('field', 'input_data')}",
                "input_type": "string",
                "required": True,
                "is_handle": is_handle_value,
                "is_list": False,
                "real_time_refresh": False,
                "advanced": False,
                "value": None,
                "options": None,
                "visibility_rules": None,
                "visibility_logic": "OR",
                "requirement_rules": None,
                "requirement_logic": "OR",
                "transition_id": start_node.get("transition_id"),
            }
            inputs.append(input_item)

        # Standard workflow outputs
        outputs = [
            {
                "name": "execution_status",
                "display_name": "Execution Status",
                "output_type": "string",
            },
            {
                "name": "workflow_execution_id",
                "display_name": "Execution ID",
                "output_type": "string",
            },
            {"name": "message", "display_name": "Message", "output_type": "string"},
        ]

        # Create workflow_info structure
        workflow_info = {
            "id": workflow_id,
            "name": workflow.get("name", ""),
            "description": workflow.get("description", ""),
            "workflow_url": workflow.get("workflow_url"),
            "builder_url": workflow.get("builder_url"),
            "start_nodes": start_nodes,
            "owner_id": workflow.get("owner_id"),
            "user_ids": [workflow.get("owner_id")] if workflow.get("owner_id") else [],
            "owner_type": "user",
            "workflow_template_id": None,
            "template_owner_id": None,
            "is_imported": False,
            "version": workflow.get("version", "1.0.0"),
            "visibility": workflow.get("visibility", "private").lower(),
            "category": workflow.get("category"),
            "tags": workflow.get("tags"),
            "status": workflow.get("status", "active"),
            "is_changes_marketplace": False,
            "is_customizable": True,
            "auto_version_on_update": False,
            "created_at": workflow.get("created_at"),
            "updated_at": workflow.get("updated_at"),
            "available_nodes": workflow.get("available_nodes") or [],
            "is_updated": True,
            "source_version_id": workflow.get("source_version_id"),
        }

        # Create the definition structure
        definition = {
            "name": f"workflow-{workflow_id}",
            "display_name": label,
            "description": label,
            "category": "Workflows",
            "icon": "Workflow",
            "beta": False,
            "path": f"workflow.{workflow_id}",
            "inputs": inputs,
            "outputs": outputs,
            "is_valid": True,
            "type": "Workflow",
            "workflow_info": workflow_info,
        }

        # Create the complete node structure
        node = {
            "id": node_id,
            "type": "WorkflowNode",
            "position": node_position,
            "data": {
                "label": label,
                "type": "component",
                "originalType": f"workflow-{workflow_id}",
                "definition": definition,
                "config": node_config,
            },
            "width": 388,
            "height": 326,
            "selected": False,
            "dragging": False,
            "style": {"opacity": 1},
        }
        node["position"] = node_info["position"]
        node["data"]["label"] = node_info["label"]
        node["data"]["config"] = node_info.get("parameters", {})
        node["width"] = node_info["dimension"]["width"]
        node["height"] = node_info["dimension"]["height"]
        return node

    except requests.RequestException as e:
        raise Exception(f"Failed to fetch workflow data from marketplace: {str(e)}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse API response: {str(e)}")
    except Exception as e:
        raise Exception(f"Error generating workflow node: {str(e)}")


# Example usage and testing
# import os

# # Example usage and testing
# if __name__ == "__main__":
#     # Create an output folder
#     os.makedirs("output_nodes", exist_ok=True)

#     # Test with the workflow IDs you were trying earlier
#     test_workflow_ids = [
#         "48c7d3e8-2c77-48c8-b248-b7bc4fb27f91"
#     ]

#     print("Testing with your workflow IDs...\n")

#     for workflow_id in test_workflow_ids:
#         try:
#             print(f"Testing workflow ID: {workflow_id}")

#             # Generate the workflow node
#             node = fulfill_workflow(workflow_id)

#             print(f"✅ Success! Generated node for: {node['data']['label']}")
#             print(f"   Node ID: {node['id']}")
#             print(f"   Inputs: {len(node['data']['definition']['inputs'])}")

#             available_nodes = node['data']['definition']['workflow_info']['available_nodes'] or []
#             print(f"   Available nodes: {len(available_nodes)}")

#             # Save output to JSON file
#             filename = f"output_nodes/workflow_node_{workflow_id}.json"
#             with open(filename, "w", encoding="utf-8") as f:
#                 json.dump(node, f, indent=2, ensure_ascii=False)

#             print(f"💾 Saved workflow node JSON to: {filename}")

#         except Exception as e:
#             print(f"❌ Failed for {workflow_id}: {str(e)}")
