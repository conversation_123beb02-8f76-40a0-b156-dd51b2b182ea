{"prompt": "Create a workflow that can give me leetcode questions everyday, it will be even better if you can add a codeblock where I can solve the question and aslo hints and solutions", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start Daily LeetCode Practice", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 80, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "CodeRunner-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "Generate Daily Question", "type": "mcp", "originalType": "MCP_Code_Runner_Mcp_execute_code_with_variables", "definition": {"name": "f506eb7c-a648-4873-8055-e2273be08478", "display_name": "Code-Runner-Mcp", "description": "Used to execute the code and returns the output. Now only supports Javascript. \nhttps://smithery.ai/server/@dravidsajinraj-iex/code-runner-mcp ", "category": null, "icon": "", "beta": false, "inputs": [{"name": "language", "display_name": "Language", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "code", "display_name": "Code", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "variables", "display_name": "Variables", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "input", "display_name": "Input", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "timeout", "display_name": "Timeout", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "memoryLimit", "display_name": "MemoryLimit", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "enableNetworking", "display_name": "EnableNetworking", "info": "", "input_type": "boolean", "input_types": ["boolean", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "hello", "display_name": "hello", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "f506eb7c-a648-4873-8055-e2273be08478", "server_path": "", "tool_name": "execute_code_with_variables", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language to execute"}, "code": {"type": "string", "description": "Code to execute"}, "variables": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "string"}], "description": "Dynamic input variables as key-value pairs. Can be a JSON object or a JSON string (e.g., {\"name\": \"<PERSON>\", \"age\": 25, \"items\": [1,2,3]} or \"{\\\"name\\\": \\\"John\\\", \\\"age\\\": 25}\")"}, "input": {"type": "string", "description": "Additional input data for the program (stdin)"}, "timeout": {"type": "number", "description": "Execution timeout in milliseconds (max 60000)"}, "memoryLimit": {"type": "number", "description": "Memory limit in MB (max 512)"}, "enableNetworking": {"type": "boolean", "description": "Enable network access for this execution"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hello": {"type": "string", "description": "hello", "title": "hello"}}}}}, "config": {"language": "python", "code": "import random\nimport json\nfrom datetime import datetime\n\n# LeetCode problems database with different difficulty levels\nleetcode_problems = [\n    {\n        \"id\": 1,\n        \"title\": \"Two Sum\",\n        \"difficulty\": \"Easy\",\n        \"description\": \"Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.\",\n        \"example\": \"Input: nums = [2,7,11,15], target = 9\\nOutput: [0,1]\\nExplanation: Because nums[0] + nums[1] == 9, we return [0, 1].\",\n        \"constraints\": \"2 <= nums.length <= 10^4\\n-10^9 <= nums[i] <= 10^9\\n-10^9 <= target <= 10^9\",\n        \"hints\": [\"Try using a hash map to store numbers you've seen.\", \"For each number, check if target - number exists in the hash map.\"],\n        \"solution\": \"def twoSum(nums, target):\\n    num_map = {}\\n    for i, num in enumerate(nums):\\n        complement = target - num\\n        if complement in num_map:\\n            return [num_map[complement], i]\\n        num_map[num] = i\\n    return []\"\n    },\n    {\n        \"id\": 121,\n        \"title\": \"Best Time to Buy and Sell Stock\",\n        \"difficulty\": \"Easy\",\n        \"description\": \"You are given an array prices where prices[i] is the price of a given stock on the ith day. You want to maximize your profit by choosing a single day to buy one stock and choosing a different day in the future to sell that stock.\",\n        \"example\": \"Input: prices = [7,1,5,3,6,4]\\nOutput: 5\\nExplanation: Buy on day 2 (price = 1) and sell on day 5 (price = 6), profit = 6-1 = 5.\",\n        \"constraints\": \"1 <= prices.length <= 10^5\\n0 <= prices[i] <= 10^4\",\n        \"hints\": [\"Keep track of the minimum price seen so far.\", \"For each price, calculate profit if sold today.\"],\n        \"solution\": \"def maxProfit(prices):\\n    min_price = float('inf')\\n    max_profit = 0\\n    for price in prices:\\n        if price < min_price:\\n            min_price = price\\n        elif price - min_price > max_profit:\\n            max_profit = price - min_price\\n    return max_profit\"\n    },\n    {\n        \"id\": 20,\n        \"title\": \"Valid Parentheses\",\n        \"difficulty\": \"Easy\",\n        \"description\": \"Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', determine if the input string is valid.\",\n        \"example\": \"Input: s = '()[]{}' Output: true\\nInput: s = '([)]' Output: false\",\n        \"constraints\": \"1 <= s.length <= 10^4\\ns consists of parentheses only '()[]{}'.\",\n        \"hints\": [\"Use a stack data structure.\", \"Push opening brackets, pop when you see closing brackets.\"],\n        \"solution\": \"def isValid(s):\\n    stack = []\\n    mapping = {')': '(', '}': '{', ']': '['}\\n    for char in s:\\n        if char in mapping:\\n            if not stack or stack.pop() != mapping[char]:\\n                return False\\n        else:\\n            stack.append(char)\\n    return not stack\"\n    },\n    {\n        \"id\": 206,\n        \"title\": \"Reverse Linked List\",\n        \"difficulty\": \"Easy\",\n        \"description\": \"Given the head of a singly linked list, reverse the list, and return the reversed list.\",\n        \"example\": \"Input: head = [1,2,3,4,5]\\nOutput: [5,4,3,2,1]\",\n        \"constraints\": \"The number of nodes in the list is the range [0, 5000].\\n-5000 <= Node.val <= 5000\",\n        \"hints\": [\"Think about using three pointers: prev, current, next.\", \"Iterate through the list and reverse the links.\"],\n        \"solution\": \"def reverseList(head):\\n    prev = None\\n    current = head\\n    while current:\\n        next_temp = current.next\\n        current.next = prev\\n        prev = current\\n        current = next_temp\\n    return prev\"\n    },\n    {\n        \"id\": 3,\n        \"title\": \"Longest Substring Without Repeating Characters\",\n        \"difficulty\": \"Medium\",\n        \"description\": \"Given a string s, find the length of the longest substring without repeating characters.\",\n        \"example\": \"Input: s = 'abcabcbb'\\nOutput: 3\\nExplanation: The answer is 'abc', with the length of 3.\",\n        \"constraints\": \"0 <= s.length <= 5 * 10^4\\ns consists of English letters, digits, symbols and spaces.\",\n        \"hints\": [\"Use sliding window technique.\", \"Keep track of characters seen and their positions.\"],\n        \"solution\": \"def lengthOfLongestSubstring(s):\\n    char_map = {}\\n    left = 0\\n    max_length = 0\\n    for right in range(len(s)):\\n        if s[right] in char_map:\\n            left = max(left, char_map[s[right]] + 1)\\n        char_map[s[right]] = right\\n        max_length = max(max_length, right - left + 1)\\n    return max_length\"\n    },\n    {\n        \"id\": 15,\n        \"title\": \"3Sum\",\n        \"difficulty\": \"Medium\",\n        \"description\": \"Given an integer array nums, return all the triplets [nums[i], nums[j], nums[k]] such that i != j, i != k, and j != k, and nums[i] + nums[j] + nums[k] == 0.\",\n        \"example\": \"Input: nums = [-1,0,1,2,-1,-4]\\nOutput: [[-1,-1,2],[-1,0,1]]\",\n        \"constraints\": \"3 <= nums.length <= 3000\\n-10^5 <= nums[i] <= 10^5\",\n        \"hints\": [\"Sort the array first.\", \"Use two pointers technique after fixing one element.\"],\n        \"solution\": \"def threeSum(nums):\\n    nums.sort()\\n    result = []\\n    for i in range(len(nums) - 2):\\n        if i > 0 and nums[i] == nums[i-1]:\\n            continue\\n        left, right = i + 1, len(nums) - 1\\n        while left < right:\\n            total = nums[i] + nums[left] + nums[right]\\n            if total < 0:\\n                left += 1\\n            elif total > 0:\\n                right -= 1\\n            else:\\n                result.append([nums[i], nums[left], nums[right]])\\n                while left < right and nums[left] == nums[left + 1]:\\n                    left += 1\\n                while left < right and nums[right] == nums[right - 1]:\\n                    right -= 1\\n                left += 1\\n                right -= 1\\n    return result\"\n    }\n]\n\n# Select a random problem for today\ntoday_seed = int(datetime.now().strftime('%Y%m%d'))\nrandom.seed(today_seed)  # Same problem for the same day\nselected_problem = random.choice(leetcode_problems)\n\nprint(json.dumps(selected_problem, indent=2))"}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 400, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "SelectData-234567890123", "type": "WorkflowNode", "position": {"x": 750, "y": 50}, "data": {"label": "Extract Problem Details", "type": "component", "originalType": "SelectDataComponent", "definition": {"name": "SelectDataComponent", "display_name": "Select Data", "description": "Extracts elements from lists or dictionaries.", "category": "Processing", "icon": "Filter", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to select from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "data_type", "display_name": "Data Type", "info": "The type of data structure to select from.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Auto-Detect", "options": ["Auto-Detect", "List", "Dictionary"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "search_mode", "display_name": "Search Mode", "info": "Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Smart Search", "options": ["Exact Path", "Smart Search"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "field_matching_mode", "display_name": "Field Matching Mode", "info": "Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Auto-detect", "options": ["Auto-detect", "Key-based Only", "Property-based Only"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "selector", "display_name": "Selector", "info": "Selector string: For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script'). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Selected Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.selectdatacomponent", "interface_issues": []}, "config": {"data_type": "Dictionary", "search_mode": "Smart Search", "field_matching_mode": "Auto-detect"}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-345678901234", "type": "WorkflowNode", "position": {"x": 1050, "y": 100}, "data": {"label": "Problem Presenter", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.3, "description": "Presents daily LeetCode problem with proper formatting", "execution_type": "response", "system_message": "You are a LeetCode practice assistant. Present the daily coding problem in a clear, structured format with:\n\n1. **Problem Title & Difficulty**\n2. **Problem Description**\n3. **Example with explanation**\n4. **Constraints**\n5. **Code Template** (provide a basic function signature)\n6. **Instructions for the user**\n\nMake it engaging and encouraging for daily practice. Include a note about accessing hints and solutions through the workflow.", "max_tokens": 1500, "autogen_agent_type": "Assistant"}}, "width": 300, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "CodeRunner-456789012345", "type": "WorkflowNode", "position": {"x": 400, "y": 300}, "data": {"label": "Code Execution Environment", "type": "mcp", "originalType": "MCP_Code_Runner_Mcp_execute_code_with_variables", "definition": {"name": "f506eb7c-a648-4873-8055-e2273be08478", "display_name": "Code-Runner-Mcp", "description": "Used to execute the code and returns the output. Now only supports Javascript. \nhttps://smithery.ai/server/@dravidsajinraj-iex/code-runner-mcp ", "category": null, "icon": "", "beta": false, "inputs": [{"name": "language", "display_name": "Language", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "code", "display_name": "Code", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "variables", "display_name": "Variables", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "input", "display_name": "Input", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "timeout", "display_name": "Timeout", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "memoryLimit", "display_name": "MemoryLimit", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "enableNetworking", "display_name": "EnableNetworking", "info": "", "input_type": "boolean", "input_types": ["boolean", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "hello", "display_name": "hello", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "f506eb7c-a648-4873-8055-e2273be08478", "server_path": "", "tool_name": "execute_code_with_variables", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language to execute"}, "code": {"type": "string", "description": "Code to execute"}, "variables": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "string"}], "description": "Dynamic input variables as key-value pairs. Can be a JSON object or a JSON string (e.g., {\"name\": \"<PERSON>\", \"age\": 25, \"items\": [1,2,3]} or \"{\\\"name\\\": \\\"John\\\", \\\"age\\\": 25}\")"}, "input": {"type": "string", "description": "Additional input data for the program (stdin)"}, "timeout": {"type": "number", "description": "Execution timeout in milliseconds (max 60000)"}, "memoryLimit": {"type": "number", "description": "Memory limit in MB (max 512)"}, "enableNetworking": {"type": "boolean", "description": "Enable network access for this execution"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hello": {"type": "string", "description": "hello", "title": "hello"}}}}}, "config": {"language": "python", "timeout": 30000, "memoryLimit": 256}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 400, "y": 300}, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-567890123456", "type": "WorkflowNode", "position": {"x": 750, "y": 300}, "data": {"label": "Hint Provider", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.5, "description": "Provides progressive hints for the current problem", "execution_type": "response", "system_message": "You are a helpful coding mentor. Based on the problem details provided, give progressive hints that guide the user toward the solution without giving it away completely. Start with high-level approach hints, then provide more specific guidance if needed. Be encouraging and educational.", "max_tokens": 800, "autogen_agent_type": "Assistant"}}, "width": 300, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-************", "type": "WorkflowNode", "position": {"x": 1100, "y": 300}, "data": {"label": "Solution Provider", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.3, "description": "Provides complete solution with explanation", "execution_type": "response", "system_message": "You are an expert coding instructor. Provide the complete solution to the LeetCode problem with:\n\n1. **Solution Code** (well-commented)\n2. **Time Complexity Analysis**\n3. **Space Complexity Analysis** \n4. **Step-by-step Explanation**\n5. **Alternative Approaches** (if applicable)\n\nMake the explanation educational and help the user understand the underlying concepts and techniques used.", "max_tokens": 1500, "autogen_agent_type": "Assistant"}}, "width": 300, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-CodeRunner-123456789012language", "source": "start-node", "sourceHandle": "flow", "target": "CodeRunner-123456789012", "targetHandle": "language", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeCodeRunner-123456789012hello-SelectData-234567890123input_data", "source": "CodeRunner-123456789012", "sourceHandle": "hello", "target": "SelectData-234567890123", "targetHandle": "input_data", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeSelectData-234567890123output_data-AgenticAI-345678901234query", "source": "SelectData-234567890123", "sourceHandle": "output_data", "target": "AgenticAI-345678901234", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeSelectData-234567890123output_data-CodeRunner-456789012345variables", "source": "SelectData-234567890123", "sourceHandle": "output_data", "target": "CodeRunner-456789012345", "targetHandle": "variables", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeSelectData-234567890123output_data-AgenticAI-567890123456query", "source": "SelectData-234567890123", "sourceHandle": "output_data", "target": "AgenticAI-567890123456", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeSelectData-234567890123output_data-AgenticAI-************query", "source": "SelectData-234567890123", "sourceHandle": "output_data", "target": "AgenticAI-************", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-SelectData-234567890123selector", "source": "start-node", "sourceHandle": "flow", "target": "SelectData-234567890123", "targetHandle": "selector", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-CodeRunner-456789012345code", "source": "start-node", "sourceHandle": "flow", "target": "CodeRunner-456789012345", "targetHandle": "code", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}