{"prompt": "Create a workflow that take the discription from user and issue the JIRA ticket and assign it to a specific person given by user.", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "User Input Collection", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_<PERSON>ra_&_Confluence_create_issue-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "Create JIRA Issue", "type": "mcp", "originalType": "MCP_Jira_&_Confluence_create_issue", "definition": {"name": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "display_name": "Jira & Confluence", "description": "Jira & Confluence mcp for managing the tickets and pages", "category": "notifications_alerts", "icon": "1751622867-jira", "beta": false, "inputs": [{"name": "summary", "display_name": "Summary", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "description", "display_name": "Description", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "issueType", "display_name": "IssueType", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "projectKey", "display_name": "ProjectKey", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "priority", "display_name": "Priority", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "assignee", "display_name": "Assignee", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "storyPoints", "display_name": "StoryPoints", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "sprintId", "display_name": "SprintId", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "bhbhjbjb", "display_name": "bhbhjbjb", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/jira.png/1751622867-jira.png", "mcp_info": {"server_id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "server_path": "", "tool_name": "create_issue", "input_schema": {"type": "object", "properties": {"summary": {"type": "string", "description": "A concise title for the issue."}, "description": {"type": "string", "description": "A detailed plain text description of the issue."}, "issueType": {"type": "string", "description": "Type of issue (e.g., 'Task')."}, "projectKey": {"type": "string", "description": "Jira project key (e.g., 'TEST')."}, "priority": {"type": "string", "description": "Priority of the issue (e.g., 'High', 'Medium', 'Low'). Case-insensitive."}, "assignee": {"type": "string", "description": "Email, username, or account ID of the user to assign the issue to (optional)."}, "storyPoints": {"type": "number", "description": "Optional story point estimate for the issue (e.g., 3, 5, 8)."}, "sprintId": {"type": "number", "description": "Optional Sprint ID to add the issue to an active sprint."}}, "required": ["summary", "issueType", "projectKey"]}, "output_schema": {"properties": {"bhbhjbjb": {"type": "string", "description": "hbjb", "title": "bhbhjbjb"}}}}, "integrations": ["742013b1-2739-43d5-9d38-391c8f3d15a8"]}, "config": {"issueType": "Task", "priority": "Medium"}, "oauthConnectionState": {}}, "width": 250, "height": 200, "selected": true, "positionAbsolute": {"x": 400, "y": 100}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodesummary-MCP_Jira_&_Confluence_create_issue-123456789012summary", "source": "start-node", "sourceHandle": "summary", "target": "MCP_<PERSON>ra_&_Confluence_create_issue-123456789012", "targetHandle": "summary", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodedescription-MCP_Jira_&_Confluence_create_issue-123456789012description", "source": "start-node", "sourceHandle": "description", "target": "MCP_<PERSON>ra_&_Confluence_create_issue-123456789012", "targetHandle": "description", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeprojectKey-MCP_Jira_&_Confluence_create_issue-123456789012projectKey", "source": "start-node", "sourceHandle": "projectKey", "target": "MCP_<PERSON>ra_&_Confluence_create_issue-123456789012", "targetHandle": "projectKey", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeassignee-MCP_Jira_&_Confluence_create_issue-123456789012assignee", "source": "start-node", "sourceHandle": "assignee", "target": "MCP_<PERSON>ra_&_Confluence_create_issue-123456789012", "targetHandle": "assignee", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}