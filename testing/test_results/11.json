{"prompt": "Create a Workflow that given a topic, a short vertical video with music and save it in the drive", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_video_script_generation_video_script_generate-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "Generate Video Script", "type": "mcp", "originalType": "MCP_video_script_generation_video_script_generate", "definition": {"name": "9d749227-a133-4307-b991-d454545bccb1", "display_name": "video-script-generation", "description": "An AI-powered server that outputs structured scenes with audio narration text and matching visual descriptions, ideal for automated video content creation.", "category": "communication", "icon": "", "beta": false, "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "video_time", "display_name": "Video time", "info": "", "input_type": "integer", "input_types": ["integer", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "scene_duration", "display_name": "Scene duration", "info": "", "input_type": "integer", "input_types": ["integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "9d749227-a133-4307-b991-d454545bccb1", "server_path": "", "tool_name": "video_script_generate", "input_schema": {"properties": {"topic": {"title": "Topic", "type": "string"}, "video_time": {"title": "Video Time", "type": "integer"}, "scene_duration": {"default": 5, "title": "Scene Duration", "type": "integer"}}, "required": ["topic", "video_time"], "title": "VideoScriptInput", "type": "object"}, "output_schema": {}}}, "config": {"video_time": 30, "scene_duration": 5}, "oauthConnectionState": {}}, "width": 300, "height": 150, "selected": true, "positionAbsolute": {"x": 400, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_stock_video_generation_mcp_generate_stock_video-123456789013", "type": "WorkflowNode", "position": {"x": 400, "y": 300}, "data": {"label": "Generate Stock Video Clips", "type": "mcp", "originalType": "MCP_stock_video_generation_mcp_generate_stock_video", "definition": {"name": "de313c10-d664-49e0-889e-e41e0230f704", "display_name": "stock-video-generation-mcp", "description": "stock video generation ", "category": "social_media", "icon": "", "beta": false, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "de313c10-d664-49e0-889e-e41e0230f704", "server_path": "", "tool_name": "generate_stock_video", "input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["script"], "title": "GenerateStockVideo", "type": "object"}, "output_schema": null}}, "config": {}, "oauthConnectionState": {}}, "width": 300, "height": 120, "selected": true, "positionAbsolute": {"x": 400, "y": 300}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_voice_generation_mcp_generate_audio-123456789014", "type": "WorkflowNode", "position": {"x": 800, "y": 100}, "data": {"label": "Generate Narration Audio", "type": "mcp", "originalType": "MCP_voice_generation_mcp_generate_audio", "definition": {"name": "068600be-4d02-4c06-a7f1-513d060cbfab", "display_name": "voice-generation-mcp", "description": "generate audio from text", "category": "social_media", "icon": "", "beta": false, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "voice_id", "display_name": "Voice id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "Provider", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_ids", "display_name": "audio_ids", "output_type": "array"}, {"name": "voice_id", "display_name": "voice_id", "output_type": "string"}, {"name": "audio_script", "display_name": "audio_script", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "068600be-4d02-4c06-a7f1-513d060cbfab", "server_path": "", "tool_name": "generate_audio", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"script": {"description": "Script is required", "maxLength": 10000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "voice_id": {"maxLength": 50, "minLength": 1, "title": "Voice Id", "type": "string"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["script", "voice_id"], "title": "GenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_ids": {"type": "array", "description": "audio ids received from Eleven labs", "title": "audio_ids"}, "voice_id": {"type": "string", "description": "voice id", "title": "voice_id"}, "audio_script": {"type": "string", "description": "audio script", "title": "audio_script"}}, "required": ["audio_ids", "voice_id", "audio_script"]}}}, "config": {"voice_id": "pNInz6obpgDQGcFmaJgB", "provider": "elevenlabs"}, "oauthConnectionState": {}}, "width": 300, "height": 150, "selected": true, "positionAbsolute": {"x": 800, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_voice_generation_mcp_generate_audio-123456789018", "type": "WorkflowNode", "position": {"x": 800, "y": 280}, "data": {"label": "Generate Background Music", "type": "mcp", "originalType": "MCP_voice_generation_mcp_generate_audio", "definition": {"name": "068600be-4d02-4c06-a7f1-513d060cbfab", "display_name": "voice-generation-mcp", "description": "generate audio from text", "category": "social_media", "icon": "", "beta": false, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "voice_id", "display_name": "Voice id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "Provider", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_ids", "display_name": "audio_ids", "output_type": "array"}, {"name": "voice_id", "display_name": "voice_id", "output_type": "string"}, {"name": "audio_script", "display_name": "audio_script", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "068600be-4d02-4c06-a7f1-513d060cbfab", "server_path": "", "tool_name": "generate_audio", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"script": {"description": "Script is required", "maxLength": 10000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "voice_id": {"maxLength": 50, "minLength": 1, "title": "Voice Id", "type": "string"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["script", "voice_id"], "title": "GenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_ids": {"type": "array", "description": "audio ids received from Eleven labs", "title": "audio_ids"}, "voice_id": {"type": "string", "description": "voice id", "title": "voice_id"}, "audio_script": {"type": "string", "description": "audio script", "title": "audio_script"}}, "required": ["audio_ids", "voice_id", "audio_script"]}}}, "config": {"voice_id": "pNInz6obpgDQGcFmaJgB", "provider": "elevenlabs"}, "oauthConnectionState": {}}, "width": 300, "height": 150, "selected": true, "positionAbsolute": {"x": 800, "y": 280}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_voice_generation_mcp_fetch_audio-123456789015", "type": "WorkflowNode", "position": {"x": 1200, "y": 100}, "data": {"label": "Fetch Narration Audio URLs", "type": "mcp", "originalType": "MCP_voice_generation_mcp_fetch_audio", "definition": {"name": "068600be-4d02-4c06-a7f1-513d060cbfab", "display_name": "voice-generation-mcp", "description": "generate audio from text", "category": "social_media", "icon": "", "beta": false, "inputs": [{"name": "audio_ids", "display_name": "Audio ids", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "Provider", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_urls", "display_name": "audio_urls", "output_type": "array"}, {"name": "mimetype", "display_name": "mimetype", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "068600be-4d02-4c06-a7f1-513d060cbfab", "server_path": "", "tool_name": "fetch_audio", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"audio_ids": {"description": "List of voice IDs is required", "items": {"type": "string"}, "minItems": 1, "title": "Audio Ids", "type": "array"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["audio_ids"], "title": "FetchGenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_urls": {"type": "array", "description": "Urls of the Audio", "title": "audio_urls"}, "mimetype": {"type": "string", "description": "Mimetype of the audio", "title": "mimetype"}}, "required": ["audio_urls", "mimetype"]}}}, "config": {"provider": "elevenlabs"}, "oauthConnectionState": {}}, "width": 300, "height": 120, "selected": true, "positionAbsolute": {"x": 1200, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_voice_generation_mcp_fetch_audio-123456789019", "type": "WorkflowNode", "position": {"x": 1200, "y": 280}, "data": {"label": "Fetch Background Music URLs", "type": "mcp", "originalType": "MCP_voice_generation_mcp_fetch_audio", "definition": {"name": "068600be-4d02-4c06-a7f1-513d060cbfab", "display_name": "voice-generation-mcp", "description": "generate audio from text", "category": "social_media", "icon": "", "beta": false, "inputs": [{"name": "audio_ids", "display_name": "Audio ids", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "Provider", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_urls", "display_name": "audio_urls", "output_type": "array"}, {"name": "mimetype", "display_name": "mimetype", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "068600be-4d02-4c06-a7f1-513d060cbfab", "server_path": "", "tool_name": "fetch_audio", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"audio_ids": {"description": "List of voice IDs is required", "items": {"type": "string"}, "minItems": 1, "title": "Audio Ids", "type": "array"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["audio_ids"], "title": "FetchGenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_urls": {"type": "array", "description": "Urls of the Audio", "title": "audio_urls"}, "mimetype": {"type": "string", "description": "Mimetype of the audio", "title": "mimetype"}}, "required": ["audio_urls", "mimetype"]}}}, "config": {"provider": "elevenlabs"}, "oauthConnectionState": {}}, "width": 300, "height": 120, "selected": true, "positionAbsolute": {"x": 1200, "y": 280}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_video_generation_mcp_generate_video-123456789016", "type": "WorkflowNode", "position": {"x": 800, "y": 500}, "data": {"label": "Generate Final Video", "type": "mcp", "originalType": "MCP_video_generation_mcp_generate_video", "definition": {"name": "56dfe8af-e982-4351-a669-0a03755b8c99", "display_name": "video-generation-mcp", "description": "video generation mcp server", "category": "communication", "icon": "", "beta": false, "inputs": [{"name": "view_type", "display_name": "View type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "stock_video_clips", "display_name": "Stock video clips", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "stock_image_clips", "display_name": "Stock image clips", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "event_stock_clips", "display_name": "Event stock clips", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "audio_urls", "display_name": "Audio urls", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "avatar_video_urls", "display_name": "Avatar video urls", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "subtitles", "display_name": "Subtitles", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "thumbnail", "display_name": "thumbnail", "output_type": "object"}, {"name": "video_link", "display_name": "video_link", "output_type": "object"}, {"name": "duration", "display_name": "duration", "output_type": "number"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "56dfe8af-e982-4351-a669-0a03755b8c99", "server_path": "", "tool_name": "generate_video", "input_schema": {"$defs": {"EventStockClip": {"properties": {"clip": {"minimum": 0, "title": "Clip", "type": "integer"}, "at_time": {"minimum": 0.0, "title": "At Time", "type": "number"}, "duration": {"exclusiveMinimum": 0.0, "title": "Duration", "type": "number"}}, "required": ["clip", "at_time", "duration"], "title": "EventStockClip", "type": "object"}, "StockImageClip": {"properties": {"at_time": {"minimum": 0.0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockImageClip", "type": "object"}, "StockVideoClip": {"properties": {"at_time": {"minimum": 0.0, "title": "At Time", "type": "number"}, "url": {"format": "uri", "maxLength": 2083, "minLength": 1, "title": "Url", "type": "string"}}, "required": ["at_time", "url"], "title": "StockVideoClip", "type": "object"}, "VideoViewType": {"enum": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "title": "VideoViewType", "type": "string"}}, "properties": {"view_type": {"$ref": "#/$defs/VideoViewType"}, "stock_video_clips": {"default": [], "items": {"$ref": "#/$defs/StockVideoClip"}, "title": "Stock Video Clips", "type": "array"}, "stock_image_clips": {"default": [], "items": {"$ref": "#/$defs/StockImageClip"}, "title": "Stock Image Clips", "type": "array"}, "event_stock_clips": {"default": [], "items": {"$ref": "#/$defs/EventStockClip"}, "title": "Event Stock Clips", "type": "array"}, "audio_urls": {"items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "avatar_video_urls": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Avatar Video Urls"}, "subtitles": {"minLength": 1, "title": "Subtitles", "type": "string"}}, "required": ["view_type", "audio_urls", "subtitles"], "title": "GenerateVideoObject", "type": "object"}, "output_schema": {"properties": {"thumbnail": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the thumbnail", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the thumbnail", "title": "mimetype"}}, "title": "thumbnail"}, "video_link": {"type": "object", "properties": {"url": {"type": "string", "description": "URL of the video", "title": "url"}, "mimetype": {"type": "string", "description": "MIME type of the video", "title": "mimetype"}}, "title": "video_link"}, "duration": {"type": "number", "description": "Duration of the video", "title": "duration"}}}}}, "config": {"view_type": "vertical", "stock_image_clips": [], "event_stock_clips": [], "avatar_video_urls": null}, "oauthConnectionState": {}}, "width": 350, "height": 200, "selected": true, "positionAbsolute": {"x": 800, "y": 500}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Google_Drive_create_file-123456789017", "type": "WorkflowNode", "position": {"x": 1200, "y": 550}, "data": {"label": "Save to Google Drive", "type": "mcp", "originalType": "MCP_Google_Drive_create_file", "definition": {"name": "1397e70d-e094-41bf-ad85-25b11a17f062", "display_name": "Google Drive", "description": "Google Drive MCP Server to interact with the google drive.", "category": "cloud_storage", "icon": "1750857244-google_drive", "beta": false, "inputs": [{"name": "name", "display_name": "Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "content", "display_name": "Content", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "mime_type", "display_name": "Mime type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "parent_folder_id", "display_name": "Parent folder id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Drive.png/1750857244-Google_Drive.png", "mcp_info": {"server_id": "1397e70d-e094-41bf-ad85-25b11a17f062", "server_path": "", "tool_name": "create_file", "input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content"}, "mime_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "text/plain", "title": "Mime Type"}, "parent_folder_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Parent Folder Id"}}, "required": ["name"], "title": "CreateFile", "type": "object"}, "output_schema": {}}, "integrations": ["9a7d4a23-9b96-45bb-976b-9db61e9a5dc9"]}, "config": {"mime_type": "video/mp4"}, "oauthConnectionState": {}}, "width": 300, "height": 150, "selected": true, "positionAbsolute": {"x": 1200, "y": 550}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_video_script_generation_video_script_generate-123456789012topic", "source": "start-node", "sourceHandle": "flow", "target": "MCP_video_script_generation_video_script_generate-123456789012", "targetHandle": "topic", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_video_script_generation_video_script_generate-123456789012script-MCP_stock_video_generation_mcp_generate_stock_video-123456789013script", "source": "MCP_video_script_generation_video_script_generate-123456789012", "sourceHandle": "script", "target": "MCP_stock_video_generation_mcp_generate_stock_video-123456789013", "targetHandle": "script", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_video_script_generation_video_script_generate-123456789012script-MCP_voice_generation_mcp_generate_audio-123456789014script", "source": "MCP_video_script_generation_video_script_generate-123456789012", "sourceHandle": "script", "target": "MCP_voice_generation_mcp_generate_audio-123456789014", "targetHandle": "script", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_video_script_generation_video_script_generate-123456789012script-MCP_voice_generation_mcp_generate_audio-123456789018script", "source": "MCP_video_script_generation_video_script_generate-123456789012", "sourceHandle": "script", "target": "MCP_voice_generation_mcp_generate_audio-123456789018", "targetHandle": "script", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_voice_generation_mcp_generate_audio-123456789014audio_ids-MCP_voice_generation_mcp_fetch_audio-123456789015audio_ids", "source": "MCP_voice_generation_mcp_generate_audio-123456789014", "sourceHandle": "audio_ids", "target": "MCP_voice_generation_mcp_fetch_audio-123456789015", "targetHandle": "audio_ids", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_voice_generation_mcp_generate_audio-123456789018audio_ids-MCP_voice_generation_mcp_fetch_audio-123456789019audio_ids", "source": "MCP_voice_generation_mcp_generate_audio-123456789018", "sourceHandle": "audio_ids", "target": "MCP_voice_generation_mcp_fetch_audio-123456789019", "targetHandle": "audio_ids", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_stock_video_generation_mcp_generate_stock_video-123456789013stock_clips-MCP_video_generation_mcp_generate_video-123456789016stock_video_clips", "source": "MCP_stock_video_generation_mcp_generate_stock_video-123456789013", "sourceHandle": "stock_clips", "target": "MCP_video_generation_mcp_generate_video-123456789016", "targetHandle": "stock_video_clips", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_voice_generation_mcp_fetch_audio-123456789015audio_urls-MCP_video_generation_mcp_generate_video-123456789016audio_urls", "source": "MCP_voice_generation_mcp_fetch_audio-123456789015", "sourceHandle": "audio_urls", "target": "MCP_video_generation_mcp_generate_video-123456789016", "targetHandle": "audio_urls", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_video_script_generation_video_script_generate-123456789012script-MCP_video_generation_mcp_generate_video-123456789016subtitles", "source": "MCP_video_script_generation_video_script_generate-123456789012", "sourceHandle": "script", "target": "MCP_video_generation_mcp_generate_video-123456789016", "targetHandle": "subtitles", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_video_generation_mcp_generate_video-123456789016video_link-MCP_Google_Drive_create_file-123456789017content", "source": "MCP_video_generation_mcp_generate_video-123456789016", "sourceHandle": "video_link", "target": "MCP_Google_Drive_create_file-123456789017", "targetHandle": "content", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_Google_Drive_create_file-123456789017name", "source": "start-node", "sourceHandle": "flow", "target": "MCP_Google_Drive_create_file-123456789017", "targetHandle": "name", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}