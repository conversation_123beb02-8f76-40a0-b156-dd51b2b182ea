{"prompt": "Generate a workflow which take in the description of process and form a diagram and mail it to a bunch of people", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 200}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Eraser_generateDiagram-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 150}, "data": {"label": "Generate Process Diagram", "type": "mcp", "originalType": "MCP_Eraser_generateDiagram", "definition": {"name": "ac2252e5-24d4-4c86-b8fc-20f13f324ce5", "display_name": "Eraser", "description": "Generate professional diagrams from text descriptions using the Eraser API through a simple MCP interface.", "category": "file_handling", "icon": "1756713912-eraser-icon-logo-png_seeklogo-483569", "beta": false, "inputs": [{"name": "prompt", "display_name": "Prompt", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "message", "display_name": "message", "output_type": "string"}, {"name": "diagramUrl", "display_name": "diagramUrl", "output_type": "string"}, {"name": "promptUsed", "display_name": "promptUsed", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/eraser-icon-logo-png_seeklogo-483569.png/1756713912-eraser-icon-logo-png_seeklogo-483569.png", "mcp_info": {"server_id": "ac2252e5-24d4-4c86-b8fc-20f13f324ce5", "server_path": "", "tool_name": "generateDiagram", "input_schema": {"type": "object", "properties": {"prompt": {"type": "string", "description": "Text description of the diagram to generate"}}, "required": ["prompt"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"type": "object", "properties": {"message": {"type": "string", "description": "A status or success message indicating the diagram generation result."}, "diagramUrl": {"type": "string", "format": "uri", "description": "The URL of the generated diagram image."}, "promptUsed": {"type": "string", "description": "The prompt or parameters used to generate the diagram."}}, "required": ["message", "diagramUrl", "promptUsed"]}}}, "config": {}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 400, "y": 150}, "dragging": false, "style": {"opacity": 1}}, {"id": "CombineTextComponent-234567890123", "type": "WorkflowNode", "position": {"x": 750, "y": 150}, "data": {"label": "Create HTML Email Body", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"num_additional_inputs": 2, "separator": "", "main_input": "<html><body><h2>Process Diagram</h2><p>Please find the generated process diagram below:</p><br><img src=\"", "input_1": "\" alt=\"Process Diagram\" style=\"max-width:100%; height:auto;\"><br><br><p>Best regards,<br>Process Automation Team</p></body></html>"}}, "width": 250, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Gmail_send_email-345678901234", "type": "WorkflowNode", "position": {"x": 1100, "y": 150}, "data": {"label": "Send Email to Recipients", "type": "mcp", "originalType": "MCP_Gmail_send_email", "definition": {"name": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "display_name": "Gmail", "description": "The server provides tools to retrieve, read, send, view, and remove emails.", "category": "notifications_alerts", "icon": "1750845179-gmail", "beta": false, "inputs": [{"name": "to", "display_name": "To", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "subject", "display_name": "Subject", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "body", "display_name": "Body", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "cc", "display_name": "Cc", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bcc", "display_name": "Bcc", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "html", "display_name": "Html", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "body", "display_name": "body", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/gmail.png/1750845179-gmail.png", "mcp_info": {"server_id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "server_path": "", "tool_name": "send_email", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "SendEmail", "type": "object"}, "output_schema": {"properties": {"body": {"type": "string", "description": "body", "title": "body"}}}}, "integrations": ["20cebfff-1435-4081-90df-90a149f41194"]}, "config": {"html": true}, "oauthConnectionState": {}}, "width": 250, "height": 160, "selected": true, "positionAbsolute": {"x": 1100, "y": 150}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeprocess_description-MCP_Eraser_generateDiagram-123456789012prompt", "source": "start-node", "sourceHandle": "process_description", "target": "MCP_Eraser_generateDiagram-123456789012", "targetHandle": "prompt", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_Eraser_generateDiagram-123456789012diagramUrl-CombineTextComponent-234567890123input_2", "source": "MCP_Eraser_generateDiagram-123456789012", "sourceHandle": "diagramUrl", "target": "CombineTextComponent-234567890123", "targetHandle": "input_2", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeCombineTextComponent-234567890123result-MCP_Gmail_send_email-345678901234body", "source": "CombineTextComponent-234567890123", "sourceHandle": "result", "target": "MCP_Gmail_send_email-345678901234", "targetHandle": "body", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeemail_recipients-MCP_Gmail_send_email-345678901234to", "source": "start-node", "sourceHandle": "email_recipients", "target": "MCP_Gmail_send_email-345678901234", "targetHandle": "to", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeemail_subject-MCP_Gmail_send_email-345678901234subject", "source": "start-node", "sourceHandle": "email_subject", "target": "MCP_Gmail_send_email-345678901234", "targetHandle": "subject", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}