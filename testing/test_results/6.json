{"prompt": "Build a workflow that starts with scraping the \"Careers\" section of a company website, identifies new job postings using a text comparison agent, and then notifies a Slack channel with a structured message summarizing the role, location, and posting date.", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start Node", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_content_extractor_mcp_scrape_web-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "Scrape Career Page", "type": "mcp", "originalType": "MCP_content_extractor_mcp_scrape_web", "definition": {"name": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "display_name": "content-extractor-mcp", "description": "generate subtitle and scrape the data", "category": "communication", "icon": "", "beta": false, "inputs": [{"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "server_path": "", "tool_name": "scrape_web", "input_schema": {"properties": {"link": {"description": "Link is required", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Link", "type": "string"}}, "required": ["link"], "title": "ScrapeWeb", "type": "object"}, "output_schema": {}}}, "config": {}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 400, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Google_Sheets_find_row-234567890123", "type": "WorkflowNode", "position": {"x": 750, "y": 100}, "data": {"label": "Get Previous Job Data", "type": "mcp", "originalType": "MCP_Google_Sheets_find_row", "definition": {"name": "345b1a31-d976-4405-b744-b0752bcc2e4c", "display_name": "Google Sheets", "description": "This MCP server integrates with your Google Sheets, to enable creating and modifying spreadsheets.", "category": "cloud_storage", "icon": "1750857479-google_sheets_logo_512px", "beta": false, "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "search_column", "display_name": "Search column", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "search_value", "display_name": "Search value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "return_first_match", "display_name": "Return first match", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Sheets_Logo_512px.png/1750857479-Google_Sheets_Logo_512px.png", "mcp_info": {"server_id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "server_path": "", "tool_name": "find_row", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}, "integrations": ["87b72e0c-e890-4ef5-bccf-7c783c1fb2bc"]}, "config": {"search_column": "A", "search_value": "previous_job_data", "return_first_match": true}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 750, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-345678901234", "type": "WorkflowNode", "position": {"x": 1100, "y": 100}, "data": {"label": "Job Comparison Agent", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.3, "description": "AI agent that compares job postings and identifies new ones", "execution_type": "response", "system_message": "You are a job posting comparison agent. Your task is to:\n1. Compare the current scraped job data with the previous job data\n2. Identify any new job postings that weren't in the previous data\n3. Extract job title, location, and posting date for each new job\n4. Return a structured JSON response with new jobs found\n\nThe current job data will be provided in the query, and the previous job data will be in input_variables.\n\nFormat your response as JSON with this structure:\n{\n  \"new_jobs\": [\n    {\n      \"title\": \"Job Title\",\n      \"location\": \"Job Location\", \n      \"posting_date\": \"Date Posted\",\n      \"description\": \"Brief description if available\"\n    }\n  ],\n  \"total_new_jobs\": number\n}", "max_tokens": 2000, "input_variables": {}}}, "width": 300, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-456789012345", "type": "WorkflowNode", "position": {"x": 1500, "y": 100}, "data": {"label": "Slack Message Formatter", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o-mini", "temperature": 0.1, "description": "Formats job posting data into structured Slack messages", "execution_type": "response", "system_message": "You are a Slack message formatter. Format the new job postings JSON data into a well-structured Slack message.\n\nCreate an engaging message that includes:\n- A header announcing new job postings\n- Each job formatted with title, location, and posting date\n- Use Slack markdown formatting for better readability\n- Include emojis to make it visually appealing\n\nExample format:\n🚨 *New Job Postings Alert!* 🚨\n\n📍 **[Job Title]** - [Location]\n📅 Posted: [Date]\n\n[Brief description if available]\n\n---\n\nIf no new jobs are found, return a message indicating no new postings were detected.", "max_tokens": 1500, "input_variables": {}}}, "width": 300, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Slack_MCP_send_channel_message-567890123456", "type": "WorkflowNode", "position": {"x": 1900, "y": 100}, "data": {"label": "Send Slack Notification", "type": "mcp", "originalType": "MCP_Slack_MCP_send_channel_message", "definition": {"name": "0730b975-db31-4861-87c1-216ac6c3c907", "display_name": "Slack MCP", "description": "This is a connector to allow <PERSON> (or any MCP client) to interact with your Slack workspace to post messages and query a list of all users.", "category": "communication", "icon": "1751446056-images", "beta": false, "inputs": [{"name": "channel_id", "display_name": "Channel id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "text", "display_name": "Text", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "thread_ts", "display_name": "Thread ts", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "as_user", "display_name": "As user", "info": "", "input_type": "boolean", "input_types": ["boolean", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/images.png/1751446056-images.png", "mcp_info": {"server_id": "0730b975-db31-4861-87c1-216ac6c3c907", "server_path": "", "tool_name": "send_channel_message", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel to send the message to", "title": "Channel Id", "type": "string"}, "text": {"description": "The message text content to send", "title": "Text", "type": "string"}, "thread_ts": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Timestamp of parent message to reply to in a thread", "title": "<PERSON><PERSON><PERSON> Ts", "type": "string"}, "as_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Send the message as the authenticated user (default: True)", "title": "As User", "type": "boolean"}}, "required": ["channel_id", "text"], "title": "SendChannelMessage", "type": "object"}, "output_schema": null}, "integrations": ["f9c18e00-5d78-44ed-9348-2bade5dde681"]}, "config": {"as_user": true}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 1900, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Google_Sheets_upsert_row-678901234567", "type": "WorkflowNode", "position": {"x": 1100, "y": 350}, "data": {"label": "Update Job Data Storage", "type": "mcp", "originalType": "MCP_Google_Sheets_upsert_row", "definition": {"name": "345b1a31-d976-4405-b744-b0752bcc2e4c", "display_name": "Google Sheets", "description": "This MCP server integrates with your Google Sheets, to enable creating and modifying spreadsheets.", "category": "cloud_storage", "icon": "1750857479-google_sheets_logo_512px", "beta": false, "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "row", "display_name": "Row", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "key_column", "display_name": "Key column", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "key_value", "display_name": "Key value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "row_id", "display_name": "row_id", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Sheets_Logo_512px.png/1750857479-Google_Sheets_Logo_512px.png", "mcp_info": {"server_id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "server_path": "", "tool_name": "upsert_row", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "key_column": {"title": "Key Column", "type": "string"}, "key_value": {"title": "Key Value", "type": "string"}}, "required": ["spreadsheet_id", "row", "key_column", "key_value"], "title": "UpsertRow", "type": "object"}, "output_schema": {"properties": {"row_id": {"type": "string", "description": "id of the row", "title": "row_id"}}}}, "integrations": ["87b72e0c-e890-4ef5-bccf-7c783c1fb2bc"]}, "config": {"key_column": "A", "key_value": "previous_job_data"}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 1100, "y": 350}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodecompany_careers_url-MCP_content_extractor_mcp_scrape_web-123456789012link", "source": "start-node", "sourceHandle": "company_careers_url", "target": "MCP_content_extractor_mcp_scrape_web-123456789012", "targetHandle": "link", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodespreadsheet_id-MCP_Google_Sheets_find_row-234567890123spreadsheet_id", "source": "start-node", "sourceHandle": "spreadsheet_id", "target": "MCP_Google_Sheets_find_row-234567890123", "targetHandle": "spreadsheet_id", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeworksheet_name-MCP_Google_Sheets_find_row-234567890123worksheet_name", "source": "start-node", "sourceHandle": "worksheet_name", "target": "MCP_Google_Sheets_find_row-234567890123", "targetHandle": "worksheet_name", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_content_extractor_mcp_scrape_web-123456789012output-AgenticAI-345678901234query", "source": "MCP_content_extractor_mcp_scrape_web-123456789012", "sourceHandle": "output", "target": "AgenticAI-345678901234", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_Google_Sheets_find_row-234567890123output-AgenticAI-345678901234input_variables", "source": "MCP_Google_Sheets_find_row-234567890123", "sourceHandle": "output", "target": "AgenticAI-345678901234", "targetHandle": "input_variables", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeAgenticAI-345678901234final_answer-AgenticAI-456789012345query", "source": "AgenticAI-345678901234", "sourceHandle": "final_answer", "target": "AgenticAI-456789012345", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeAgenticAI-456789012345final_answer-MCP_Slack_MCP_send_channel_message-567890123456text", "source": "AgenticAI-456789012345", "sourceHandle": "final_answer", "target": "MCP_Slack_MCP_send_channel_message-567890123456", "targetHandle": "text", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeslack_channel_id-MCP_Slack_MCP_send_channel_message-567890123456channel_id", "source": "start-node", "sourceHandle": "slack_channel_id", "target": "MCP_Slack_MCP_send_channel_message-567890123456", "targetHandle": "channel_id", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_content_extractor_mcp_scrape_web-123456789012output-MCP_Google_Sheets_upsert_row-678901234567row", "source": "MCP_content_extractor_mcp_scrape_web-123456789012", "sourceHandle": "output", "target": "MCP_Google_Sheets_upsert_row-678901234567", "targetHandle": "row", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodespreadsheet_id-MCP_Google_Sheets_upsert_row-678901234567spreadsheet_id", "source": "start-node", "sourceHandle": "spreadsheet_id", "target": "MCP_Google_Sheets_upsert_row-678901234567", "targetHandle": "spreadsheet_id", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeworksheet_name-MCP_Google_Sheets_upsert_row-678901234567worksheet_name", "source": "start-node", "sourceHandle": "worksheet_name", "target": "MCP_Google_Sheets_upsert_row-678901234567", "targetHandle": "worksheet_name", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}