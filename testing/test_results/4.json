{"prompt": "Please generate a workflow that starts with a candidate's resume link. The workflow should then analyze the resume for suitability before sending the analysis results to an API endpoint via a POST request. The API request should have a 10-second timeout.", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_content_extractor_mcp_scrape_web-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "Resume Content Extractor", "type": "mcp", "originalType": "MCP_content_extractor_mcp_scrape_web", "definition": {"name": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "display_name": "content-extractor-mcp", "description": "generate subtitle and scrape the data", "category": "communication", "icon": "", "beta": false, "inputs": [{"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "server_path": "", "tool_name": "scrape_web", "input_schema": {"properties": {"link": {"description": "Link is required", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Link", "type": "string"}}, "required": ["link"], "title": "ScrapeWeb", "type": "object"}, "output_schema": {}}}, "config": {}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 400, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-234567890123", "type": "WorkflowNode", "position": {"x": 750, "y": 100}, "data": {"label": "Resume Suitability Analyzer", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.3, "description": "AI agent to analyze resume content for candidate suitability", "execution_type": "response", "system_message": "You are a professional HR specialist tasked with analyzing resumes to evaluate candidate suitability. Analyze the provided resume content and provide a comprehensive assessment covering:\n\n1. Relevant skills and experience\n2. Education background alignment\n3. Career progression and growth\n4. Overall suitability score (1-10)\n5. Key strengths and areas of concern\n6. Recommendation (Highly Suitable/Suitable/Not Suitable)\n\nProvide your analysis in a structured JSON format with clear sections for each evaluation criteria.", "max_tokens": 2000}}, "width": 280, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "ApiRequestNode-345678901234", "type": "WorkflowNode", "position": {"x": 1100, "y": 100}, "data": {"label": "Send Analysis to API", "type": "component", "originalType": "ApiRequestNode", "definition": {"name": "ApiRequestNode", "display_name": "API Request", "description": "Makes a single HTTP request to the specified URL.", "category": "Data Interaction", "icon": "Globe", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "url", "display_name": "URL", "info": "The URL to make the request to.", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "method", "display_name": "HTTP Method", "info": "The HTTP method to use for the request.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "GET", "options": ["GET", "POST", "PUT", "PATCH", "DELETE"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query_params", "display_name": "Query Parameters", "info": "Key-value pairs to append to the URL query string (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "headers", "display_name": "Request Headers", "info": "Key-value pairs for request headers (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "body", "display_name": "Request Body (JSON)", "info": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "requirement_logic": "OR"}], "outputs": [{"name": "data", "display_name": "Response Data/Body", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "status_code", "display_name": "Status Code", "output_type": "int", "semantic_type": null, "method": null}, {"name": "response_headers", "display_name": "Response Headers", "output_type": "dict", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.data interaction.apirequestnode", "interface_issues": []}, "config": {"method": "POST", "headers": {"Content-Type": "application/json", "Accept": "application/json", "X-Request-Timeout": "10"}}}, "width": 250, "height": 140, "selected": false, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_content_extractor_mcp_scrape_web-123456789012link", "source": "start-node", "sourceHandle": "flow", "target": "MCP_content_extractor_mcp_scrape_web-123456789012", "targetHandle": "link", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_content_extractor_mcp_scrape_web-123456789012extracted_content-AgenticAI-234567890123query", "source": "MCP_content_extractor_mcp_scrape_web-123456789012", "sourceHandle": "extracted_content", "target": "AgenticAI-234567890123", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeAgenticAI-234567890123final_answer-ApiRequestNode-345678901234body", "source": "AgenticAI-234567890123", "sourceHandle": "final_answer", "target": "ApiRequestNode-345678901234", "targetHandle": "body", "type": "default", "selected": false}]}, "validation": {"is_valid": false, "missing_fields": [{"node_id": "ApiRequestNode-345678901234", "node_label": "Send Analysis to API", "input_name": "component_validation", "input_display_name": "Component Validation", "is_handle": false}, {"node_id": "ApiRequestNode-345678901234", "node_label": "Send Analysis to API", "input_name": "url", "input_display_name": "URL", "is_handle": true}], "errors": ["Request body is required for POST, PUT, and PATCH methods"], "warnings": [], "error": null}}