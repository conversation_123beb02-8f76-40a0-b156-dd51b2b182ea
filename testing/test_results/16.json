{"prompt": "Ingest product manuals PDFs, extract key safety information, and alert via Slack on critical warnings.", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start Node", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Website_Generator_read_file-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "PDF Reader", "type": "mcp", "originalType": "MCP_Website_Generator_read_file", "definition": {"name": "796faf92-17eb-4c8f-bd22-185418425862", "display_name": "Website Generator", "description": "Website Generator MCP Server.", "category": null, "icon": "", "beta": false, "inputs": [{"name": "file_path", "display_name": "File path", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "796faf92-17eb-4c8f-bd22-185418425862", "server_path": "", "tool_name": "read_file", "input_schema": {"properties": {"file_path": {"title": "File Path", "type": "string"}}, "required": ["file_path"], "title": "read_fileArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "read_fileOutput", "type": "object"}}}, "config": {}, "oauthConnectionState": {}}, "width": 300, "height": 150, "selected": true, "positionAbsolute": {"x": 400, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-234567890123", "type": "WorkflowNode", "position": {"x": 800, "y": 100}, "data": {"label": "Safety Information Extractor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.3, "description": "AI agent specialized in extracting safety information from product manuals", "execution_type": "response", "max_tokens": 2000, "system_message": "You are a safety information extraction specialist. Your task is to:\n1. Carefully read and analyze product manual content\n2. Extract ALL safety-related information including warnings, cautions, notices, and safety instructions\n3. Identify and categorize safety information by severity level\n4. Return the extracted information in a structured JSON format with fields: 'general_safety', 'critical_warnings', 'cautions', 'notices'\n5. For critical warnings, include specific hazard descriptions and potential consequences\n\nFocus on identifying:\n- DANGER/WARNING/CAUTION labels\n- Safety precautions and procedures\n- Hazard identification\n- Personal protective equipment requirements\n- Emergency procedures\n- Risk mitigation measures", "input_variables": {}}}, "width": 400, "height": 250, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-345678901234", "type": "WorkflowNode", "position": {"x": 1300, "y": 100}, "data": {"label": "Critical Warning Analyzer", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.1, "description": "AI agent specialized in identifying critical safety warnings that require immediate attention", "execution_type": "response", "max_tokens": 1500, "system_message": "You are a critical safety warning classifier. Your task is to:\n1. Analyze the extracted safety information\n2. Identify warnings that are CRITICAL and require immediate attention\n3. Classify warnings based on severity: CRITICAL, HIGH, MEDIUM, LOW\n4. Return a JSON response with: {'has_critical_warnings': true/false, 'critical_count': number, 'critical_warnings': [list of critical warnings], 'severity_analysis': 'detailed analysis'}\n\nCritical warnings typically involve:\n- Life-threatening hazards\n- Serious injury potential\n- Fire/explosion risks\n- Toxic exposure\n- Electrical hazards\n- Equipment damage that could cause harm\n- Emergency shutdown procedures\n\nReturn 'CRITICAL_ALERT' in your response if any critical warnings are found.", "input_variables": {}}}, "width": 400, "height": 250, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "ConditionalNode-456789012345", "type": "WorkflowNode", "position": {"x": 1800, "y": 100}, "data": {"label": "Critical Warning Decision", "type": "component", "originalType": "ConditionalNode", "definition": {"name": "ConditionalNode", "display_name": "Switch-Case Router", "description": "Evaluates multiple conditions and routes data to matching outputs", "category": "Logic", "icon": "GitBranch", "beta": false, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "Input data that will be routed when conditions match. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "source", "display_name": "Data Source", "info": "Select the data source for condition evaluation: 'node_output' uses connected input data, 'global_context' uses a direct global context value.", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "global_context_value", "display_name": "Global Context Value", "info": "The actual value from global context to evaluate conditions against (e.g., 'premium', 'basic', 'admin'). This value will be compared directly against each condition's expected value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "condition_1_operator", "display_name": "Condition 1 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_1_expected_value", "display_name": "Condition 1 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_1_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_1_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_conditions", "display_name": "Number of Additional Conditions", "info": "Number of additional conditions beyond the base 1 condition (0-9).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "evaluation_strategy", "display_name": "Evaluation Strategy", "info": "Strategy for handling multiple conditions: 'first_match' stops at the first true condition, 'all_matches' executes all true conditions in parallel.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "all_matches", "options": ["first_match", "all_matches"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_operator", "display_name": "Condition 2 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_expected_value", "display_name": "Condition 2 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}, {"field_name": "condition_2_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_2_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_global_context_value", "display_name": "Condition 2 - Global Context Value", "info": "The specific global context value to use for condition 2 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_operator", "display_name": "Condition 3 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_expected_value", "display_name": "Condition 3 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}, {"field_name": "condition_3_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_3_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_global_context_value", "display_name": "Condition 3 - Global Context Value", "info": "The specific global context value to use for condition 3 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_operator", "display_name": "Condition 4 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_expected_value", "display_name": "Condition 4 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}, {"field_name": "condition_4_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_4_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_global_context_value", "display_name": "Condition 4 - Global Context Value", "info": "The specific global context value to use for condition 4 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_operator", "display_name": "Condition 5 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_expected_value", "display_name": "Condition 5 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}, {"field_name": "condition_5_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_5_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_global_context_value", "display_name": "Condition 5 - Global Context Value", "info": "The specific global context value to use for condition 5 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_operator", "display_name": "Condition 6 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_expected_value", "display_name": "Condition 6 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}, {"field_name": "condition_6_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_6_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_global_context_value", "display_name": "Condition 6 - Global Context Value", "info": "The specific global context value to use for condition 6 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_operator", "display_name": "Condition 7 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_expected_value", "display_name": "Condition 7 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}, {"field_name": "condition_7_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_7_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_global_context_value", "display_name": "Condition 7 - Global Context Value", "info": "The specific global context value to use for condition 7 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_operator", "display_name": "Condition 8 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_expected_value", "display_name": "Condition 8 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}, {"field_name": "condition_8_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_8_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_global_context_value", "display_name": "Condition 8 - Global Context Value", "info": "The specific global context value to use for condition 8 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_operator", "display_name": "Condition 9 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_expected_value", "display_name": "Condition 9 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}, {"field_name": "condition_9_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_9_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_global_context_value", "display_name": "Condition 9 - Global Context Value", "info": "The specific global context value to use for condition 9 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_operator", "display_name": "Condition 10 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_expected_value", "display_name": "Condition 10 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}, {"field_name": "condition_10_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_10_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_global_context_value", "display_name": "Condition 10 - Global Context Value", "info": "The specific global context value to use for condition 10 evaluation (e.g., 'admin', 'premium', 'basic'). This allows each condition to use a different global context value. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}, {"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "default", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.logic.conditionalnode", "interface_issues": []}, "config": {"source": "node_output", "condition_1_operator": "contains", "condition_1_expected_value": "CRITICAL_ALERT", "num_additional_conditions": 0, "evaluation_strategy": "first_match"}}, "width": 350, "height": 200, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "CombineTextComponent-567890123456", "type": "WorkflowNode", "position": {"x": 2250, "y": 50}, "data": {"label": "Critical Alert Message Builder", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"separator": "__NEWLINE____NEWLINE__", "input_1": "🚨 CRITICAL SAFETY ALERT 🚨", "input_2": "Critical safety warnings detected in product manual!", "input_3": "__NEWLINE__📋 Analysis Results:", "number_of_inputs": 4}}, "width": 350, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "CombineTextComponent-789012345678", "type": "WorkflowNode", "position": {"x": 2250, "y": 300}, "data": {"label": "Summary Report Builder", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"separator": "__NEWLINE____NEWLINE__", "input_1": "✅ Product Manual Safety Analysis Complete", "input_2": "No critical warnings requiring immediate attention were found.", "input_3": "__NEWLINE__📋 Safety Summary:", "number_of_inputs": 4}}, "width": 350, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Slack_MCP_send_channel_message-678901234567", "type": "WorkflowNode", "position": {"x": 2700, "y": 50}, "data": {"label": "Critical <PERSON><PERSON>", "type": "mcp", "originalType": "MCP_Slack_MCP_send_channel_message", "definition": {"name": "0730b975-db31-4861-87c1-216ac6c3c907", "display_name": "Slack MCP", "description": "This is a connector to allow <PERSON> (or any MCP client) to interact with your Slack workspace to post messages and query a list of all users.", "category": "communication", "icon": "1751446056-images", "beta": false, "inputs": [{"name": "channel_id", "display_name": "Channel id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "text", "display_name": "Text", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "thread_ts", "display_name": "Thread ts", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "as_user", "display_name": "As user", "info": "", "input_type": "boolean", "input_types": ["boolean", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/images.png/1751446056-images.png", "mcp_info": {"server_id": "0730b975-db31-4861-87c1-216ac6c3c907", "server_path": "", "tool_name": "send_channel_message", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel to send the message to", "title": "Channel Id", "type": "string"}, "text": {"description": "The message text content to send", "title": "Text", "type": "string"}, "thread_ts": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Timestamp of parent message to reply to in a thread", "title": "<PERSON><PERSON><PERSON> Ts", "type": "string"}, "as_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Send the message as the authenticated user (default: True)", "title": "As User", "type": "boolean"}}, "required": ["channel_id", "text"], "title": "SendChannelMessage", "type": "object"}, "output_schema": null}, "integrations": ["f9c18e00-5d78-44ed-9348-2bade5dde681"]}, "config": {"as_user": true}, "oauthConnectionState": {}}, "width": 350, "height": 200, "selected": true, "positionAbsolute": {"x": 2700, "y": 50}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Slack_MCP_send_channel_message-890123456789", "type": "WorkflowNode", "position": {"x": 2700, "y": 300}, "data": {"label": "Summary Report Slack Sender", "type": "mcp", "originalType": "MCP_Slack_MCP_send_channel_message", "definition": {"name": "0730b975-db31-4861-87c1-216ac6c3c907", "display_name": "Slack MCP", "description": "This is a connector to allow <PERSON> (or any MCP client) to interact with your Slack workspace to post messages and query a list of all users.", "category": "communication", "icon": "1751446056-images", "beta": false, "inputs": [{"name": "channel_id", "display_name": "Channel id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "text", "display_name": "Text", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "thread_ts", "display_name": "Thread ts", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "as_user", "display_name": "As user", "info": "", "input_type": "boolean", "input_types": ["boolean", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/images.png/1751446056-images.png", "mcp_info": {"server_id": "0730b975-db31-4861-87c1-216ac6c3c907", "server_path": "", "tool_name": "send_channel_message", "input_schema": {"properties": {"channel_id": {"description": "The ID of the channel to send the message to", "title": "Channel Id", "type": "string"}, "text": {"description": "The message text content to send", "title": "Text", "type": "string"}, "thread_ts": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Timestamp of parent message to reply to in a thread", "title": "<PERSON><PERSON><PERSON> Ts", "type": "string"}, "as_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Send the message as the authenticated user (default: True)", "title": "As User", "type": "boolean"}}, "required": ["channel_id", "text"], "title": "SendChannelMessage", "type": "object"}, "output_schema": null}, "integrations": ["f9c18e00-5d78-44ed-9348-2bade5dde681"]}, "config": {"as_user": true}, "oauthConnectionState": {}}, "width": 350, "height": 200, "selected": true, "positionAbsolute": {"x": 2700, "y": 300}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_Website_Generator_read_file-123456789012file_path", "source": "start-node", "sourceHandle": "flow", "target": "MCP_Website_Generator_read_file-123456789012", "targetHandle": "file_path", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_Website_Generator_read_file-123456789012result-AgenticAI-234567890123query", "source": "MCP_Website_Generator_read_file-123456789012", "sourceHandle": "result", "target": "AgenticAI-234567890123", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeAgenticAI-234567890123final_answer-AgenticAI-345678901234query", "source": "AgenticAI-234567890123", "sourceHandle": "final_answer", "target": "AgenticAI-345678901234", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeAgenticAI-345678901234final_answer-ConditionalNode-456789012345input_data", "source": "AgenticAI-345678901234", "sourceHandle": "final_answer", "target": "ConditionalNode-456789012345", "targetHandle": "input_data", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeConditionalNode-456789012345condition_1_output-CombineTextComponent-567890123456input_4", "source": "ConditionalNode-456789012345", "sourceHandle": "condition_1_output", "target": "CombineTextComponent-567890123456", "targetHandle": "input_4", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeConditionalNode-456789012345default-CombineTextComponent-789012345678input_4", "source": "ConditionalNode-456789012345", "sourceHandle": "default", "target": "CombineTextComponent-789012345678", "targetHandle": "input_4", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeCombineTextComponent-567890123456combined_text-MCP_Slack_MCP_send_channel_message-678901234567text", "source": "CombineTextComponent-567890123456", "sourceHandle": "combined_text", "target": "MCP_Slack_MCP_send_channel_message-678901234567", "targetHandle": "text", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeCombineTextComponent-789012345678combined_text-MCP_Slack_MCP_send_channel_message-890123456789text", "source": "CombineTextComponent-789012345678", "sourceHandle": "combined_text", "target": "MCP_Slack_MCP_send_channel_message-890123456789", "targetHandle": "text", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_Slack_MCP_send_channel_message-678901234567channel_id", "source": "start-node", "sourceHandle": "flow", "target": "MCP_Slack_MCP_send_channel_message-678901234567", "targetHandle": "channel_id", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_Slack_MCP_send_channel_message-890123456789channel_id", "source": "start-node", "sourceHandle": "flow", "target": "MCP_Slack_MCP_send_channel_message-890123456789", "targetHandle": "channel_id", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}