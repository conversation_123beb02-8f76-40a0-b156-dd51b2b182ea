{"prompt": "Generate a workflow which send me a random quote from Bible to me via Slack and email", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 200}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Bible_MCP_get_random_bible_verse-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 200}, "data": {"label": "Get Random Bible Verse", "type": "mcp", "originalType": "MCP_Bible_MCP_get_random_bible_verse", "definition": {"name": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "display_name": "Bible MCP", "description": "Get Bible related information", "category": "communication", "icon": "1752583131-81me58a46fl", "beta": false, "inputs": [{"name": "book_ids", "display_name": "Book ids", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/81Me58A46fL._UF1000%2C1000_QL80_.jpg/1752583131-81Me58A46fL._UF10001000_QL80_.jpg", "mcp_info": {"server_id": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "server_path": "", "tool_name": "get_random_bible_verse", "input_schema": {"properties": {"book_ids": {"default": null, "title": "Book Ids", "type": "string"}}, "title": "get_random_bible_verseArguments", "type": "object"}, "output_schema": null}}, "config": {}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 400, "y": 200}, "dragging": false, "style": {"opacity": 1}}, {"id": "CombineTextComponent-234567890123", "type": "WorkflowNode", "position": {"x": 750, "y": 200}, "data": {"label": "Format Bible Message", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"main_input": "🙏 Daily Bible Verse 🙏", "num_additional_inputs": 2, "separator": "__NEWLINE____NEWLINE__", "input_2": "__NEWLINE____NEWLINE__May this verse bless your day! ✨"}}, "width": 250, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Slack_MCP_send_direct_message-345678901234", "type": "WorkflowNode", "position": {"x": 1100, "y": 100}, "data": {"label": "Send Slack Message", "type": "mcp", "originalType": "MCP_Slack_MCP_send_direct_message", "definition": {"name": "0730b975-db31-4861-87c1-216ac6c3c907", "display_name": "Slack MCP", "description": "This is a connector to allow <PERSON> (or any MCP client) to interact with your Slack workspace to post messages and query a list of all users.", "category": "communication", "icon": "1751446056-images", "beta": false, "inputs": [{"name": "user_id", "display_name": "User id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "text", "display_name": "Text", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "as_user", "display_name": "As user", "info": "", "input_type": "boolean", "input_types": ["boolean", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "user name", "display_name": "user name", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/images.png/1751446056-images.png", "mcp_info": {"server_id": "0730b975-db31-4861-87c1-216ac6c3c907", "server_path": "", "tool_name": "send_direct_message", "input_schema": {"properties": {"user_id": {"description": "The ID of the user to send the direct message to", "title": "User Id", "type": "string"}, "text": {"description": "The message text content to send", "title": "Text", "type": "string"}, "as_user": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Send the message as the authenticated user (default: True)", "title": "As User", "type": "boolean"}}, "required": ["user_id", "text"], "title": "SendDirectMessage", "type": "object"}, "output_schema": {"properties": {"user name": {"type": "string", "description": "user name", "title": "user name"}}}}, "integrations": ["f9c18e00-5d78-44ed-9348-2bade5dde681"]}, "config": {"as_user": true}, "oauthConnectionState": {}}, "width": 250, "height": 140, "selected": true, "positionAbsolute": {"x": 1100, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Gmail_send_email-456789012345", "type": "WorkflowNode", "position": {"x": 1100, "y": 300}, "data": {"label": "Send Email", "type": "mcp", "originalType": "MCP_Gmail_send_email", "definition": {"name": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "display_name": "Gmail", "description": "The server provides tools to retrieve, read, send, view, and remove emails.", "category": "notifications_alerts", "icon": "1750845179-gmail", "beta": false, "inputs": [{"name": "to", "display_name": "To", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "subject", "display_name": "Subject", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "body", "display_name": "Body", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "cc", "display_name": "Cc", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bcc", "display_name": "Bcc", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "html", "display_name": "Html", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "body", "display_name": "body", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/gmail.png/1750845179-gmail.png", "mcp_info": {"server_id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "server_path": "", "tool_name": "send_email", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "cc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Cc"}, "bcc": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bcc"}, "html": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Html"}}, "required": ["to", "subject", "body"], "title": "SendEmail", "type": "object"}, "output_schema": {"properties": {"body": {"type": "string", "description": "body", "title": "body"}}}}, "integrations": ["20cebfff-1435-4081-90df-90a149f41194"]}, "config": {"subject": "Your Daily Bible Verse", "html": false}, "oauthConnectionState": {}}, "width": 250, "height": 160, "selected": true, "positionAbsolute": {"x": 1100, "y": 300}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_Bible_MCP_get_random_bible_verse-123456789012book_ids", "source": "start-node", "sourceHandle": "flow", "target": "MCP_Bible_MCP_get_random_bible_verse-123456789012", "targetHandle": "book_ids", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_Bible_MCP_get_random_bible_verse-123456789012result-CombineTextComponent-234567890123input_1", "source": "MCP_Bible_MCP_get_random_bible_verse-123456789012", "sourceHandle": "result", "target": "CombineTextComponent-234567890123", "targetHandle": "input_1", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeCombineTextComponent-234567890123result-MCP_Slack_MCP_send_direct_message-345678901234text", "source": "CombineTextComponent-234567890123", "sourceHandle": "result", "target": "MCP_Slack_MCP_send_direct_message-345678901234", "targetHandle": "text", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeCombineTextComponent-234567890123result-MCP_Gmail_send_email-456789012345body", "source": "CombineTextComponent-234567890123", "sourceHandle": "result", "target": "MCP_Gmail_send_email-456789012345", "targetHandle": "body", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeuser_id-MCP_Slack_MCP_send_direct_message-345678901234user_id", "source": "start-node", "sourceHandle": "user_id", "target": "MCP_Slack_MCP_send_direct_message-345678901234", "targetHandle": "user_id", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeemail_address-MCP_Gmail_send_email-456789012345to", "source": "start-node", "sourceHandle": "email_address", "target": "MCP_Gmail_send_email-456789012345", "targetHandle": "to", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}