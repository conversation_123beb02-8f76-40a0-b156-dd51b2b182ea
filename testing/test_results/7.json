{"prompt": "Design a workflow that takes LinkedIn profile URLs as input, fetches their public information, then uses an AI agent to create a standardized candidate summary. The results should then be pushed into a Google Sheet for recruitment tracking.", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 50, "y": 200}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 80, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_content_extractor_mcp_scrape_web-123456789012", "type": "WorkflowNode", "position": {"x": 350, "y": 200}, "data": {"label": "Extract LinkedIn Profile", "type": "mcp", "originalType": "MCP_content_extractor_mcp_scrape_web", "definition": {"name": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "display_name": "content-extractor-mcp", "description": "generate subtitle and scrape the data", "category": "communication", "icon": "", "beta": false, "inputs": [{"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "server_path": "", "tool_name": "scrape_web", "input_schema": {"properties": {"link": {"description": "Link is required", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Link", "type": "string"}}, "required": ["link"], "title": "ScrapeWeb", "type": "object"}, "output_schema": {}}}, "config": {}, "oauthConnectionState": {}}, "width": 250, "height": 120, "selected": true, "positionAbsolute": {"x": 350, "y": 200}, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-234567890123", "type": "WorkflowNode", "position": {"x": 700, "y": 200}, "data": {"label": "Generate Candidate Su<PERSON>ry", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.3, "description": "AI agent for creating standardized candidate summaries", "execution_type": "response", "system_message": "You are a recruitment specialist AI. Analyze the LinkedIn profile data and create a candidate summary with EXACTLY these fields separated by pipe (|) characters on a single line:\n\nFull Name|Current Position|Company|Years of Experience|Top 3 Skills|Education|Industry|Key Achievement|Contact Info|Score (1-10)|Recruitment Notes\n\nExample format:\n<PERSON>|Senior Developer|Tech Corp|8|Python,JavaScript,AWS|BS Computer Science - MIT|Technology|Led team of 5 developers|<EMAIL>|8|Strong technical background\n\nEnsure each field is filled with relevant information from the profile data. If information is not available, use 'N/A'.", "max_tokens": 1500, "termination_condition": "", "input_variables": {}, "autogen_agent_type": "Assistant"}}, "width": 280, "height": 160, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Code_Runner_Mcp_execute_code_with_variables-345678901234", "type": "WorkflowNode", "position": {"x": 1080, "y": 200}, "data": {"label": "Format Data for Sheets", "type": "mcp", "originalType": "MCP_Code_Runner_Mcp_execute_code_with_variables", "definition": {"name": "f506eb7c-a648-4873-8055-e2273be08478", "display_name": "Code-Runner-Mcp", "description": "Used to execute the code and returns the output. Now only supports Javascript. \nhttps://smithery.ai/server/@dravidsajinraj-iex/code-runner-mcp ", "category": null, "icon": "", "beta": false, "inputs": [{"name": "language", "display_name": "Language", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "code", "display_name": "Code", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "variables", "display_name": "Variables", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "input", "display_name": "Input", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "timeout", "display_name": "Timeout", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "memoryLimit", "display_name": "MemoryLimit", "info": "", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "enableNetworking", "display_name": "EnableNetworking", "info": "", "input_type": "boolean", "input_types": ["boolean", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "hello", "display_name": "hello", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "f506eb7c-a648-4873-8055-e2273be08478", "server_path": "", "tool_name": "execute_code_with_variables", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language to execute"}, "code": {"type": "string", "description": "Code to execute"}, "variables": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "string"}], "description": "Dynamic input variables as key-value pairs. Can be a JSON object or a JSON string (e.g., {\"name\": \"<PERSON>\", \"age\": 25, \"items\": [1,2,3]} or \"{\\\"name\\\": \\\"John\\\", \\\"age\\\": 25}\")"}, "input": {"type": "string", "description": "Additional input data for the program (stdin)"}, "timeout": {"type": "number", "description": "Execution timeout in milliseconds (max 60000)"}, "memoryLimit": {"type": "number", "description": "Memory limit in MB (max 512)"}, "enableNetworking": {"type": "boolean", "description": "Enable network access for this execution"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hello": {"type": "string", "description": "hello", "title": "hello"}}}}}, "config": {"language": "python", "code": "# Convert pipe-separated summary to array for Google Sheets\nif 'summary' in variables:\n    summary_text = variables['summary']\n    # Split by pipe and clean up each field\n    row_data = [field.strip() for field in summary_text.split('|')]\n    print(row_data)\nelse:\n    print(['Error: No summary data provided'])", "timeout": 10000, "memoryLimit": 128, "enableNetworking": false}, "oauthConnectionState": {}}, "width": 250, "height": 140, "selected": true, "positionAbsolute": {"x": 1080, "y": 200}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_Google_Sheets_add_single_row-456789012345", "type": "WorkflowNode", "position": {"x": 1430, "y": 200}, "data": {"label": "Add to Recruitment Sheet", "type": "mcp", "originalType": "MCP_Google_Sheets_add_single_row", "definition": {"name": "345b1a31-d976-4405-b744-b0752bcc2e4c", "display_name": "Google Sheets", "description": "This MCP server integrates with your Google Sheets, to enable creating and modifying spreadsheets.", "category": "cloud_storage", "icon": "1750857479-google_sheets_logo_512px", "beta": false, "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "row", "display_name": "Row", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "row_index", "display_name": "Row index", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "insert_mode", "display_name": "Insert mode", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "Error", "display_name": "Error", "output_type": "string"}, {"name": "message", "display_name": "message", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Sheets_Logo_512px.png/1750857479-Google_Sheets_Logo_512px.png", "mcp_info": {"server_id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "server_path": "", "tool_name": "add_single_row", "input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Row Index"}, "insert_mode": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Insert Mode"}}, "required": ["spreadsheet_id", "row"], "title": "AddSingleRow", "type": "object"}, "output_schema": {"properties": {"Error": {"type": "string", "description": "error", "title": "Error"}, "message": {"type": "string", "description": "message", "title": "message"}}}}, "integrations": ["87b72e0c-e890-4ef5-bccf-7c783c1fb2bc"]}, "config": {"insert_mode": false}, "oauthConnectionState": {}}, "width": 250, "height": 140, "selected": true, "positionAbsolute": {"x": 1430, "y": 200}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodelinkedin_url-MCP_content_extractor_mcp_scrape_web-123456789012link", "source": "start-node", "sourceHandle": "linkedin_url", "target": "MCP_content_extractor_mcp_scrape_web-123456789012", "targetHandle": "link", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_content_extractor_mcp_scrape_web-123456789012text-AgenticAI-234567890123query", "source": "MCP_content_extractor_mcp_scrape_web-123456789012", "sourceHandle": "text", "target": "AgenticAI-234567890123", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeAgenticAI-234567890123final_answer-MCP_Code_Runner_Mcp_execute_code_with_variables-345678901234variables", "source": "AgenticAI-234567890123", "sourceHandle": "final_answer", "target": "MCP_Code_Runner_Mcp_execute_code_with_variables-345678901234", "targetHandle": "variables", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_Code_Runner_Mcp_execute_code_with_variables-345678901234hello-MCP_Google_Sheets_add_single_row-456789012345row", "source": "MCP_Code_Runner_Mcp_execute_code_with_variables-345678901234", "sourceHandle": "hello", "target": "MCP_Google_Sheets_add_single_row-456789012345", "targetHandle": "row", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodespreadsheet_id-MCP_Google_Sheets_add_single_row-456789012345spreadsheet_id", "source": "start-node", "sourceHandle": "spreadsheet_id", "target": "MCP_Google_Sheets_add_single_row-456789012345", "targetHandle": "spreadsheet_id", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeworksheet_name-MCP_Google_Sheets_add_single_row-456789012345worksheet_name", "source": "start-node", "sourceHandle": "worksheet_name", "target": "MCP_Google_Sheets_add_single_row-456789012345", "targetHandle": "worksheet_name", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}