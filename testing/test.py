import json
import os

import pandas as pd
import requests

df = pd.read_csv("Test Prompt - Sheet1.csv")
workflow_generation_url = "http://127.0.0.1:8000/api/v1/generate_workflow"
validation_url = (
    "https://app-dev.rapidinnovation.dev/api/v1/components/validate_workflow"
)
os.makedirs("test_results", exist_ok=True)
for index, row in df.iterrows():
    if row["tested"] == 1:
        continue
    task = row["Prompt"]
    payload = {"task": task}
    try:
        response = requests.post(workflow_generation_url, json=payload)
    except:
        continue
    data = response.json()
    workflow = data["workflow"]
    payload = workflow
    try:
        response = requests.post(validation_url, json=payload)
    except:
        continue
    data = response.json()
    with open(f"test_results/{index}.json", "w") as f:
        json.dump(
            {"prompt": task, "workflow": workflow, "validation": data}, f, indent=2
        )
    df.loc[index, "tested"] = 1
    df.to_csv("Test Prompt - Sheet1.csv", index=False)
    break
