{"prompt": "Craft a workflow to create a short video script from a given topic, with a specific duration of one minute. Use this script to generate audio, and then fetch the generated audio file.", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_video_script_generation_video_script_generate-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "Generate Video Script", "type": "mcp", "originalType": "MCP_video_script_generation_video_script_generate", "definition": {"name": "9d749227-a133-4307-b991-d454545bccb1", "display_name": "video-script-generation", "description": "An AI-powered server that outputs structured scenes with audio narration text and matching visual descriptions, ideal for automated video content creation.", "category": "communication", "icon": "", "beta": false, "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "video_time", "display_name": "Video time", "info": "", "input_type": "integer", "input_types": ["integer", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "scene_duration", "display_name": "Scene duration", "info": "", "input_type": "integer", "input_types": ["integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "Any"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "9d749227-a133-4307-b991-d454545bccb1", "server_path": "", "tool_name": "video_script_generate", "input_schema": {"properties": {"topic": {"title": "Topic", "type": "string"}, "video_time": {"title": "Video Time", "type": "integer"}, "scene_duration": {"default": 5, "title": "Scene Duration", "type": "integer"}}, "required": ["topic", "video_time"], "title": "VideoScriptInput", "type": "object"}, "output_schema": {}}}, "config": {"video_time": 60, "scene_duration": 5}, "oauthConnectionState": {}}, "width": 250, "height": 150, "selected": true, "positionAbsolute": {"x": 400, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_voice_generation_mcp_generate_audio-234567890123", "type": "WorkflowNode", "position": {"x": 750, "y": 100}, "data": {"label": "Generate Audio", "type": "mcp", "originalType": "MCP_voice_generation_mcp_generate_audio", "definition": {"name": "068600be-4d02-4c06-a7f1-513d060cbfab", "display_name": "voice-generation-mcp", "description": "generate audio from text", "category": "social_media", "icon": "", "beta": false, "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "voice_id", "display_name": "Voice id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "Provider", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_ids", "display_name": "audio_ids", "output_type": "array"}, {"name": "voice_id", "display_name": "voice_id", "output_type": "string"}, {"name": "audio_script", "display_name": "audio_script", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "068600be-4d02-4c06-a7f1-513d060cbfab", "server_path": "", "tool_name": "generate_audio", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"script": {"description": "Script is required", "maxLength": 10000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "voice_id": {"maxLength": 50, "minLength": 1, "title": "Voice Id", "type": "string"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["script", "voice_id"], "title": "GenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_ids": {"type": "array", "description": "audio ids received from Eleven labs", "title": "audio_ids"}, "voice_id": {"type": "string", "description": "voice id", "title": "voice_id"}, "audio_script": {"type": "string", "description": "audio script", "title": "audio_script"}}, "required": ["audio_ids", "voice_id", "audio_script"]}}}, "config": {"provider": "elevenlabs"}, "oauthConnectionState": {}}, "width": 250, "height": 150, "selected": true, "positionAbsolute": {"x": 750, "y": 100}, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_voice_generation_mcp_fetch_audio-345678901234", "type": "WorkflowNode", "position": {"x": 1100, "y": 100}, "data": {"label": "Fetch Audio File", "type": "mcp", "originalType": "MCP_voice_generation_mcp_fetch_audio", "definition": {"name": "068600be-4d02-4c06-a7f1-513d060cbfab", "display_name": "voice-generation-mcp", "description": "generate audio from text", "category": "social_media", "icon": "", "beta": false, "inputs": [{"name": "audio_ids", "display_name": "Audio ids", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "Provider", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_urls", "display_name": "audio_urls", "output_type": "array"}, {"name": "mimetype", "display_name": "mimetype", "output_type": "string"}], "is_valid": true, "type": "MCP", "logo": null, "mcp_info": {"server_id": "068600be-4d02-4c06-a7f1-513d060cbfab", "server_path": "", "tool_name": "fetch_audio", "input_schema": {"$defs": {"VoiceProvider": {"enum": ["elevenlabs", "playht"], "title": "VoiceProvider", "type": "string"}}, "properties": {"audio_ids": {"description": "List of voice IDs is required", "items": {"type": "string"}, "minItems": 1, "title": "Audio Ids", "type": "array"}, "provider": {"$ref": "#/$defs/VoiceProvider", "default": "elevenlabs", "description": "Optional voice provider platform"}}, "required": ["audio_ids"], "title": "FetchGenerateAudio", "type": "object"}, "output_schema": {"type": "object", "properties": {"audio_urls": {"type": "array", "description": "Urls of the Audio", "title": "audio_urls"}, "mimetype": {"type": "string", "description": "Mimetype of the audio", "title": "mimetype"}}, "required": ["audio_urls", "mimetype"]}}}, "config": {"provider": "elevenlabs"}, "oauthConnectionState": {}}, "width": 250, "height": 150, "selected": true, "positionAbsolute": {"x": 1100, "y": 100}, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_video_script_generation_video_script_generate-123456789012topic", "source": "start-node", "sourceHandle": "flow", "target": "MCP_video_script_generation_video_script_generate-123456789012", "targetHandle": "topic", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-MCP_voice_generation_mcp_generate_audio-234567890123voice_id", "source": "start-node", "sourceHandle": "flow", "target": "MCP_voice_generation_mcp_generate_audio-234567890123", "targetHandle": "voice_id", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_video_script_generation_video_script_generate-123456789012script-MCP_voice_generation_mcp_generate_audio-234567890123script", "source": "MCP_video_script_generation_video_script_generate-123456789012", "sourceHandle": "script", "target": "MCP_voice_generation_mcp_generate_audio-234567890123", "targetHandle": "script", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeMCP_voice_generation_mcp_generate_audio-234567890123audio_ids-MCP_voice_generation_mcp_fetch_audio-345678901234audio_ids", "source": "MCP_voice_generation_mcp_generate_audio-234567890123", "sourceHandle": "audio_ids", "target": "MCP_voice_generation_mcp_fetch_audio-345678901234", "targetHandle": "audio_ids", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}