{"prompt": "Run an Investment Thesis document through a parser AI, convert the output JSON to structured data, and trigger a market research workflow on the extracted companies.", "workflow": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "IO", "icon": "Play", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.io.startnode", "interface_issues": []}, "config": {}}, "width": 200, "height": 100, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-123456789012", "type": "WorkflowNode", "position": {"x": 400, "y": 100}, "data": {"label": "Investment Thesis Parser AI", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "visible_in_logs_ui": true, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Agent execution mode (response only).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "response", "options": ["response"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"model_provider": "OpenAI", "model_name": "gpt-4o", "temperature": 0.3, "description": "AI agent that parses Investment Thesis documents and extracts key information in JSON format", "execution_type": "response", "system_message": "You are an expert Investment Thesis document parser. Your task is to analyze the provided Investment Thesis document and extract key information in a structured JSON format. Focus on identifying:\n\n1. Company names and entities mentioned\n2. Investment rationale and key points\n3. Market sectors and industries\n4. Financial metrics and projections\n5. Risk factors\n6. Investment timeline and targets\n\nReturn the extracted information as a valid JSON object with clear field names and structured data that can be easily processed by downstream systems. Ensure all company names are clearly identified in a 'companies' array with each company as a separate string element.\n\nExample output format:\n{\n  \"companies\": [\"Company A\", \"Company B\", \"Company C\"],\n  \"investment_rationale\": \"...\",\n  \"market_sectors\": [...],\n  \"financial_metrics\": {...},\n  \"risk_factors\": [...],\n  \"investment_timeline\": \"...\"\n}", "max_tokens": 2000, "input_variables": {}}}, "width": 300, "height": 200, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "UniversalConverterComponent-234567890123", "type": "WorkflowNode", "position": {"x": 800, "y": 100}, "data": {"label": "JSON to Structured Data Converter", "type": "component", "originalType": "UniversalConverterComponent", "definition": {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "description": "Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)", "category": "Processing", "icon": "ArrowRightLeft", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to convert. Can be any type - string, object, array, number, etc. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "from_type", "display_name": "From Type", "info": "The current type of your input data. Auto-detect will determine this automatically.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Auto-detect", "options": ["Auto-detect", "String", "Number", "Boolean", "Object", "Array", "<PERSON><PERSON>"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "to_type", "display_name": "To Type", "info": "The target type to convert to. JSON String: pretty-formatted JSON. CSV String: comma-separated values. Joined String: array elements joined with delimiter. Split Array: string split by delimiter. Flattened Object: nested object flattened to dot notation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "String", "options": ["String", "Number", "Boolean", "Object", "Array", "JSON String", "CSV String", "Joined String", "Split Array", "Flattened Object"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "delimiter", "display_name": "Delimiter", "info": "Delimiter for CSV/Join/Split operations (e.g., ',', ';', '|', ' ', '\\t' for tab, '\\n' for newline)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ",", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "pretty_format", "display_name": "Pretty Format", "info": "For JSON String output, use pretty formatting with indentation and line breaks", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "converted_data", "display_name": "Converted Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "original_type", "display_name": "Original Type", "output_type": "string", "semantic_type": null, "method": null}, {"name": "target_type", "display_name": "Target Type", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.universalconvertercomponent", "interface_issues": []}, "config": {"from_type": "String", "to_type": "Object", "pretty_format": true}}, "width": 300, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "SelectDataComponent-345678901234", "type": "WorkflowNode", "position": {"x": 1200, "y": 100}, "data": {"label": "Extract Companies List", "type": "component", "originalType": "SelectDataComponent", "definition": {"name": "SelectDataComponent", "display_name": "Select Data", "description": "Extracts elements from lists or dictionaries.", "category": "Processing", "icon": "Filter", "beta": false, "requires_approval": false, "visible_in_logs_ui": false, "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to select from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "data_type", "display_name": "Data Type", "info": "The type of data structure to select from.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Auto-Detect", "options": ["Auto-Detect", "List", "Dictionary"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "search_mode", "display_name": "Search Mode", "info": "Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Smart Search", "options": ["Exact Path", "Smart Search"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "field_matching_mode", "display_name": "Field Matching Mode", "info": "Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Auto-detect", "options": ["Auto-detect", "Key-based Only", "Property-based Only"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "selector", "display_name": "Selector", "info": "Selector string: For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script'). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Selected Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.selectdatacomponent", "interface_issues": []}, "config": {"data_type": "Auto-Detect", "search_mode": "Smart Search", "field_matching_mode": "Auto-detect", "selector": "companies"}}, "width": 280, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "workflow-4a1ba435-cb33-4e3b-9539-9b40ae30aca6-1758173670111", "type": "WorkflowNode", "position": {"x": 1600, "y": 100}, "data": {"label": "Market Research Workflow", "type": "component", "originalType": "workflow-4a1ba435-cb33-4e3b-9539-9b40ae30aca6", "definition": {"name": "workflow-4a1ba435-cb33-4e3b-9539-9b40ae30aca6", "display_name": "Market Research Workflow", "description": "Market Research Workflow", "category": "Workflows", "icon": "Workflow", "beta": false, "path": "workflow.4a1ba435-cb33-4e3b-9539-9b40ae30aca6", "inputs": [{"name": "query", "display_name": "Query", "info": "Input field: query", "input_type": "string", "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "transition_id": "transition-AgenticAI-1752768337944"}], "outputs": [{"name": "execution_status", "display_name": "Execution Status", "output_type": "string"}, {"name": "workflow_execution_id", "display_name": "Execution ID", "output_type": "string"}, {"name": "message", "display_name": "Message", "output_type": "string"}], "is_valid": true, "type": "Workflow", "workflow_info": {"id": "4a1ba435-cb33-4e3b-9539-9b40ae30aca6", "name": "Company Research Workflow", "description": "Company_Research_Workflow", "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3d4f756a-ebdf-4e88-9c12-dc9185c8d94e.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/985e1f6f-22b5-4e4a-a44f-cb8df9424b5d.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752768337944"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "user_ids": ["d3b78018-fe5a-4dd4-8148-d533ab3d8a93"], "owner_type": "user", "workflow_template_id": null, "template_owner_id": null, "is_imported": false, "version": "1.1.0", "visibility": "public", "category": null, "tags": null, "status": "active", "is_changes_marketplace": false, "is_customizable": true, "auto_version_on_update": false, "created_at": "2025-07-18T07:53:40.739024", "updated_at": "2025-08-22T04:57:25.917929", "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752768337944", "label": "AI Agent Executor"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752770571139", "label": "AI Agent Executor"}], "is_updated": true, "source_version_id": "d5ba6c53-53ec-4dce-b73e-236b6e968a58"}}, "config": {}}, "width": 300, "height": 150, "selected": false, "dragging": false, "style": {"opacity": 1}}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgestart-nodeflow-AgenticAI-123456789012query", "source": "start-node", "sourceHandle": "flow", "target": "AgenticAI-123456789012", "targetHandle": "query", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeAgenticAI-123456789012final_answer-UniversalConverterComponent-234567890123input_data", "source": "AgenticAI-123456789012", "sourceHandle": "final_answer", "target": "UniversalConverterComponent-234567890123", "targetHandle": "input_data", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeUniversalConverterComponent-234567890123converted_data-SelectDataComponent-345678901234input_data", "source": "UniversalConverterComponent-234567890123", "sourceHandle": "converted_data", "target": "SelectDataComponent-345678901234", "targetHandle": "input_data", "type": "default", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "zIndex": 5}, "id": "reactflow__edgeSelectDataComponent-345678901234output_data-workflow-456789012345query", "source": "SelectDataComponent-345678901234", "sourceHandle": "output_data", "target": "workflow-456789012345", "targetHandle": "query", "type": "default", "selected": false}]}, "validation": {"is_valid": true, "missing_fields": [], "errors": [], "warnings": [], "error": null}}