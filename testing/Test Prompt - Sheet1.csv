ID,Prompt,tested
1,"Generate the workflow which check my calender, process them with ai and send me the summery on the mail.",1.0
2,"Generate the workflow which given a topic, research on the internet with perplexity and compile a eassy and save it in a new document.",1.0
3,Generate the workflow which do the question of my homework and send the answer on my slack,1.0
4,"Craft a workflow to create a short script using ai, maybe a song. Use this script to generate audio, and then fetch the generated audio file.",1.0
5,Please generate a workflow that starts with a candidate's resume link. The workflow should then analyze the resume for suitability before sending the analysis results to an API endpoint via a POST request. The API request should have a 10-second timeout.,1.0
6,"Create a workflow that ingests a list of company names from a CSV file, enriches each company with Crunchbase and LinkedIn data, then runs sentiment analysis on recent news about them. The enriched dataset should be saved into a PostgreSQL database.",1.0
7,"Build a workflow that starts with scraping the ""Careers"" section of a company website, identifies new job postings using a text comparison agent, and then notifies a Slack channel with a structured message summarizing the role, location, and posting date.",1.0
8,"Design a workflow that takes LinkedIn profile URLs as input, fetches their public information, then uses an AI agent to create a standardized candidate summary. The results should then be pushed into a Google Sheet for recruitment tracking.",1.0
9,"Develop a workflow that monitors a Google Drive folder for new research PDFs, parses them with a ""Document Summarizer"" agent, and appends the summaries with metadata into a Notion database.",1.0
10,"Create a workflow where user input is a topic or keyword. The workflow should fetch relevant recent articles from Google News, extract key insights using an AI summarizer, and output a weekly digest email formatted in Markdown.",1.0
11,"Design a workflow that schedules daily monitoring of Twitter/X for specific hashtags, runs sentiment analysis on collected tweets, creates a visualization of trends, and saves the report into Google Slides.",1.0
12,"Create a Workflow that given a topic, a short vertical video with music and save it in the drive",1.0
13,Create a workflow that take the discription from user and issue the JIRA ticket and assign it to a specific person given by user.,1.0
14,"Build a workflow where user input is a conference name. The system scrapes the conference website, extracts the sponsors and speaker list, retrieves additional background for each entity from LinkedIn, and assembles a competitive intelligence report.",1.0
15,"Run an Investment Thesis document through a parser AI, convert the output JSON to structured data, and trigger a market research workflow on the extracted companies.",1.0
16,"Extract financial metrics from SEC filings PDFs using OCR, then feed data to an AI agent for risk evaluation and scoring.",1.0
17,"Ingest product manuals PDFs, extract key safety information, and alert via Slack on critical warnings.",1.0
18,"Create a workflow that accepts a JSON input containing company names and automatically retrieves in-depth company profiles by aggregating funding history, executive bios, market news, and core product details from internal or public APIs. Then generate an AI summary report for each company and save all data in a structured database.",1.0
19,"Develop a parallel workflow that takes ten user profile JSON objects, processes each profile concurrently through an AI agent to analyze skills, accomplishments, and relevant certifications, then aggregates all results into individual detailed reports. Finally, append all reports to a specified Google Document.",1.0
20,Generate a workflow which send me a random quote from Bible to me via Slack and email,1.0
21,Generate a workflow which take in the description of process and form a diagram and mail it to a bunch of people,1.0
22,Generate me a worklflow that will give me news of the world daily,1.0
23,"Generate me a workflow that will read my resume, go through my telegram group and select the job oppurtunities posted there that match my resume and send it to me in my mail",1.0
24,"Create a workflow that can give me leetcode questions everyday, it will be even better if you can add a codeblock where I can solve the question and aslo hints and solutions",1.0
25,"Develop an automated system that tracks employee time logs submitted via forms, calculates overtime and compliance with labor policies, generates payroll input files, and emails detailed reports to HR with summarized exceptions flagged.",1.0
26,"Create a health monitoring workflow that ingests patient wearable device data, applies AI algorithms to detect health anomalies, triggers alerts to healthcare providers, and compiles patient wellness summaries for periodic evaluations.",0.0
27,"Generate the workflow which takes a keyword, search through the github, pass the information of top five results to an ai and summeries about it.",0.0
28,Generate a workflow which taken in a description and create a website for me with images if required.,0.0
29,Generate a workflow which could take information from user and form a survay form for user.,0.0
30,Generate a workflow which take in a topic and create a memo on it and send it back to my slack account.,0.0
31,generate a workflow which send the message of any special occasion of a person in the general group of slack. take the information of the dates of special occasion via a google sheet,0.0
32,generate a workflow which takes in the transcript of a meeting and send a summery of it to a list of emails.,0.0
33,"Generate a workflow which taken in a Database and makes a analytic report, create a new document, put the report in it.",0.0
34,"Generate a workflow which reads all the email of last 24 hours and seperate out based on importance, work, ugency. filter out the non important mail and send the summery to the slack",0.0
35,Generate a workflow which generate a random story and store it in google docs,0.0
36,"generate a workflow which takes in the characters, timeline, main idea and theme of a story and creates a story, and store it in a google docs. use multiple agent for different purposes",0.0
37,"Generate a workflow which takes in a document id of a google docs, read it and send the summory to the slack",0.0
38,"Generate a workflow which creates a story, timeline and generate the comic",0.0
39,Generate a PPT based on a document which user give.,0.0
40,"Generate a workflow which takes in a punch to documents, summeries all the content and send them to email it.",0.0
41,"Create a cinematic video generation pipeline. The pipeline should start by generating a script, then iterate through each scene to generate a corresponding image and video. Finally, it should compile the generated videos, audio, and subtitles into a complete cinematic video.",0.0
42,"Create a workflow that starts by generating structured marketing content for a pitch deck using a search tool or URL fetch, then selects an appropriate presentation template based on the content's tone and domain. Next, format the content into slide-ready JSON with specified slide counts and vivid descriptions, ensuring logical segmentation and persuasive language. Finally, generate a PowerPoint presentation slide-by-slide using the formatted JSON and selected template, returning a downloadable presentation URL",0.0
43,"Create a workflow that generates a professional job description for a Senior Full Stack Engineer using provided job data, fetches company details from Google Sheets, and formats the output as a structured JSON object. The process should store the job description in a new Google Doc, update the spreadsheet with the document URL, and add the job data to a designated worksheet. Ensure the workflow uses Markdown formatting for the document, incorporates a company logo, and maintains SEO-friendly, concise content tailored to the company\u2019s culture and industry.",0.0
44,"Create a workflow that generates ADS based on a bunch of documents, and description from users",0.0
45,Create a Random mean every day and send it to my slack,0.0
46,"Create a workflow that first extracts all LinkedIn URLs from a given JSON input. The next step should take the extracted URLs and use them to conduct a deep research and profiling report on the associated companies, including funding, leadership, and recent news.",0.0
47,Generate a workflow which takes bunch of documents and save the summeries about it and save it in a new documents,0.0
48,Generate a workflow which takes a google sheet and generate a analysical report on it and send it via slacks,0.0
49,Generate a workflow deletes a bunch of google docs give by a user,0.0
50,Generate a workflow which takes is mails from last 2 days and create a story out of it and a video on it.,0.0
51,Generate a workflow which take in a prompt and generate a complete repo on github with proper code.,0.0
52,Generate a workflow which Create a static website on a given topic,0.0
53,Generate a workflow which create an email based on the discription of the User and send it to a bunch of people.,0.0
54,Generate a workflow which writes a resume for a given document which contain everything about the candidate,0.0
55,Generate a workflow which describes a image for you and Generate an interesting story around it.,0.0
56,Generate a workflow which take a image and prompt and generate new image base on the prompt and images,0.0
57,"Generate a workflow which takes in a company linkedin, go over all the employees and create a analytic report on the company",0.0
58,"Generate a workflow which could takes in codes, check for error and push to corrected code on a predefined repo",0.0
59,Generate a workflow which takes in a jupyter notebook and create a report on google docs,0.0
60,Generate a workflow which taken in a url to the website of a story and rate it from 1 to 10,0.0
61,Generate s workflow which Generate a workflow for n8n platform. Use multiple agents for different task related to it and save the workflows in a github repository,0.0
62,"Generate a workflow which act as a teacher and explain everything you want to ask via slack, input and output both comes from slack",0.0
63,"Generate a workflow which send me a random Joke to my slack everyday at noon, 2pm and 4pm",0.0
64,Generate a workflow Whcih could write a fanficion given a fandom and a brief summery about the fanfiction,0.0
65,Generate a workflow to create a new document which contain the letter which is described by the user,0.0
66,Generate a workflow which diagnose your will based on the symptoms you was showing,0.0
67,Generate a workflow which delete every file in the list and send the successful message on slack,0.0
68,Generate a workflow which gererate the images of the celebrities ,0.0
69,"Generate a workflow which create an video based on a story which is also generated by workflow. the only thing given by user is theme, title, storyline.",0.0
70,Generate a workflow which send the summery of last week from a specific email and send it to my slack,0.0
71,"Generate a workflow which given a project description, develop different task about it and create the jira tickit for each task",0.0
72,"Generate a workflow which given the information, schedule meeting and send the mail about it to appropriate people.",0.0
73,Generate a workflow which read all incoming meetings and send the summery of each meeting to my slack every day at 10 am,0.0
74,Generate a workflow which takes in a list of phone numbers and send a message to them which is generate via ai,0.0
75,"Generate a workflow which taken in a list of name, debt ammount and number and call each on of them with a custom message to give debt faster.",0.0
76,Generate a workflow which taken in a description of a scene and write a entire script for that scene and save it in a document,0.0
77,Generate a workflow which search top 10 AI Techonologies and generate a webpage related to it and save it in the Github,0.0
78,Generate a workflow which search though all the meeting of a perticular day and retrun the time slot for a new meeting on my slack,0.0
79,Generate a workflow which create a manga page based on description given by the users,0.0
80,Generate a workflow which takes in an unfinished suduko and solve it and return to the gmail,0.0
81,Generate a workflow which takens in current state of tic-tac-toc and send back the best next move on slack,0.0
82,Generate a workflow which create a maze and send it on email,0.0
83,"Generate a workflow which play chess with me on slack, user tell his move, workflow genereate the moved pieces and then make this own move, and generate now position of the board and send it back. make is come and for",0.0
84,Generate a workflow which takes in a google sheet and generate a apperoate graphs of it.,0.0
