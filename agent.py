import json
import os

from strands import Agent
from strands.agent.agent_result import Agent<PERSON><PERSON>ult
from strands.models.openai import OpenAIModel
from strands.multiagent import MultiAgentBase, MultiAgentResult
from strands.multiagent.base import MultiAgentBase, MultiAgentResult, NodeResult, Status
from strands.telemetry.metrics import EventLoopMetrics
from strands.tools import tool
from strands.types.content import ContentBlock, Message

from . import tools
from .helper import safe_loads

# import tools

REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")

model = OpenAIModel(
    client_args={
        "api_key": REQUESTY_API_KEY,
        "base_url": "https://router.requesty.ai/v1",
    },
    # **model_config
    model_id="bedrock/anthropic/claude-4-sonnet-latest",
)

reasoning_model = OpenAIModel(
    client_args={
        "api_key": REQUESTY_API_KEY,
        "base_url": "https://router.requesty.ai/v1",
    },
    model_id="bedrock/anthropic/claude-4-sonnet-latest",
    reasoning_effort="medium",
)


def get_callback_logger(agent_name, logger):
    def callback_logger(**kwargs):
        kwargs["agent"] = agent_name
        if "message" in kwargs:
            logger.info(kwargs)
        elif "reasoning" in kwargs:
            logger.info(kwargs)
        elif "result" in kwargs:
            logger.info(kwargs)

    return callback_logger


class PostProcessingNode(MultiAgentBase):
    def __init__(self, logger):
        super().__init__()
        self.logger = logger
        self.name = "post_processing"
        self.func = tools.post_processing

    async def invoke_async(self, task, **kwargs):
        event_loop = EventLoopMetrics()
        attri = event_loop.start_cycle()
        task = "\n".join(t["text"] for t in task)
        if "```json" in task:
            task = task.split("```json")[1].split("```")[0]
        task = safe_loads(task)
        if "workflow" in task:
            print(task["workflow"])
            task["workflow"] = self.func(task["workflow"])
        task = json.dumps(task)
        result = f"```json\n{task}\n```"
        self.logger.info(
            json.dumps(
                {
                    "message": {"role": "assistant", "content": result},
                    "agent": "post_processing",
                }
            )
        )
        event_loop.end_cycle(*attri)
        agent_result = AgentResult(
            stop_reason="end_turn",
            message=Message(role="assistant", content=[ContentBlock(text=result)]),
            metrics=event_loop,
            state=None,
        )
        # Return wrapped in MultiAgentResult
        return MultiAgentResult(
            status=Status.COMPLETED,
            results={self.name: NodeResult(result=agent_result)},
        )


prompt_enhancement_system_prompt = """
System Prompt Enhancer

Description:
    This system prompt takes a user-provided prompt and enhances it by:
    - Clarifying ambiguous statements.
    - Structuring the prompt logically.
    - Extracting criteria and conditions explicitly.
    - Converting the prompt into a machine-readable format for downstream processing.

Rules:
    1. Do not introduce new criteria, requirements, or conditions that are not present in the user-provided prompt. 
       Only enhance clarity, grammar, structure, and readability.
       
Functionality:
    1. Clarification:
        - Rewrites vague or ambiguous text for clearer understanding.
        - Ensures instructions are precise and actionable.

    2. Criteria Extraction:
        - Identifies specific requirements, constraints, or conditions mentioned in the prompt.
        - Separates them into individual, clearly labeled criteria.

    3. Machine-Readable Structuring:
        - Converts the clarified prompt and extracted criteria into structured formats
          such as JSON or dictionaries with clearly defined fields.
        - Example structure:
            {
                "original_prompt": "<original user prompt>",
                "clarified_prompt": "<enhanced readable prompt>",
                "criteria": [
                    {
                        "condition": "<specific condition>",
                        "requirement": "<requirement to satisfy>"
                    },
                    ...
                ]
            }
"""
workflow_generation_system_prompt = """
You are a Workflow Generation Agent.  
Your primary task is to generate workflows in strict JSON format containing "nodes" and "edges".  
You must always output in the following schema:

```json
{
  "workflow": { ... valid JSON workflow ... }   // Only if a workflow is generated
  "message": "..."                             // Natural language explanation, guidance, or answer
}```

ensure that the output is in ```joan

```
-------------------------------------------------------------------------------

0. Pre-Processing Rule  
------------------------------------------------
1. Carefully analyze the user request.  
2. If the request is suitable for workflow generation:  
   - Divide the purpose or task into simple subtasks.  
   - Separate the criteria/conditions that must be satisfied.  
   - Verify that all subtasks and criteria are satisfied before returning the final workflow JSON.  
   - Output inside the `"workflow"` key.  
3. If the request is ambiguous or incomplete:  
   - Ask clarifying questions in `"message"`.  
4. If the request is unrelated to workflows:  
   - Output only `"message"`, no `"workflow"`.  

-------------------------------------------------------------------------------

1. Nodes  
--------
Each node must include the following properties:

- "node_id": A unique identifier string in the format: <NodeType>-<12 digit randomnumber>.  
- "label": The name of the node (not type, not description).  
- "OriginalType": The exact node type.  
- "type": The general type of the node. ["component", "workflow", "mcp"]
- "position": An object with "x" and "y" coordinates (both integers).  
- "dimension": An object with "width" and "height" (both integers).  
- "parameters": A JSON object containing node-specific parameters.
- "mcp_id": The MCP ID, only if the node type is mcp.
- "workflow_id": The workflow ID, only if the node type is workflow."
- "tool_name": The name of the tool, only if the node type is mcp.

Important Rules for Parameters:  
- If an input is required, it must either:  
  • Have a value in "parameters", OR  
  • Have a handle connected by an edge — but never both.  
- Inputs of the workflow must NOT be hardcoded unless explicitly specified by the user.  
- All required inputs of the workflow must be **requested from the user via the StartNode prompt**.  
- Do not create new parameters — only use parameters valid for the node type.  
- You may add extra nodes if required for completeness, error handling, or robustness.  

There is always only one StartNode with:
"id": "start-node"
and the StartNode has no parameters.

-------------------------------------------------------------------------------

2. Edges  
--------
Each edge must connect two nodes and include:

- "source": The ID of the source node.  
- "sourceHandle": The output handle name from the source node.  
- "target": The ID of the target node.  
- "targetHandle": The input handle name of the target node.  

Important Rules for Edges:  
- Edges are only formed from output handles → input handles.  
- Never use generic "input" or "output".  
- If a node requires multiple inputs, create separate edges for each input.  
- Any input of nodes that needs to be taken from the user must be connected to the StartNode flow handle.  
- The flow must always begin from the StartNode.
- The data type of both sourceHandle and targerHandle must be the same.

-------------------------------------------------------------------------------

3. Output Requirement  
---------------------
- Always wrap responses in the schema:

{
  "workflow": { ... valid JSON workflow ... },   // Only if workflow is generated
  "message": "..."                               // Always allowed
}

- `"workflow"` must be omitted if no workflow is generated.  
- `"message"` must contain natural text to explain, clarify, or respond.  
- Never produce malformed JSON.  
- Never include comments inside the JSON.  

-------------------------------------------------------------------------------

4. User Instruction Priority Rule  
---------------------------------
- If the user specifies a certain way the workflow should be constructed 
  (e.g., structure, ordering, naming, parameter handling, edge design), 
  follow those instructions exactly, unless they conflict with the strict JSON format or rules above.  

-------------------------------------------------------------------------------

5. Robustness Rule  
---------------------------------
- Always prioritize returning a complete, valid workflow JSON when possible.  
- If the input is vague, ambiguous, or incomplete: ask clarifying questions in `"message"`.  
- If the input is impossible (e.g., violates logic or technical limits), explain clearly in `"message"`.  
- If the input is unrelated to workflows, provide a natural answer in `"message"` only.  
- Never output broken JSON.  
- Always ensure graceful fallback.  
"""
validator_system_prompt = """
You are a workflow validation system. 
Your job is to check whether a given workflow definition is structurally valid JSON 
and whether it completely fulfills the user's prompt.

Validation Rules
================

1. JSON Structure
-----------------
- The workflow must be valid JSON.
- The workflow must contain the required top-level fields:
  • "nodes": list of node objects
  • "edges": list of edge objects
- Each "node" must at least have a unique "node_id" and a "label".
- Each "edge" must at least have "source" and "target".
- No isolated nodes: every node must be connected into the graph.
- No dangling edges: every edge must connect existing nodes.

2. Functional Equivalence
-------------------------
- The workflow must fulfill **all requirements of the user prompt**.  
- This includes:
  • Required inputs or parameters mentioned in the prompt.  
  • Any constraints, conditions, or transformations requested by the user.  
  • The correct sequence or flow of operations implied by the prompt.  
- It is not enough for the workflow to be partially correct — it must be fully aligned with the prompt.  
- Parameters may be omitted only if defaults exist AND this does not break the prompt’s intent.  
- Extra nodes/steps are allowed if they improve robustness or handle errors, but they must not alter the meaning or functionality.  

Output Format
=============
Always return the result in the following format (inside a fenced JSON block):


```json
{
  "valid": bool,            # true if the workflow is valid and fulfills the user prompt
  "errors": [               # list of rule violations or missing elements
    "string error message 1",
    "string error message 2"
  ],
  "warnings": [             # non-critical suggestions or best-practice notices
    "string warning message 1",
    "string warning message 2"
  ]
}
```
If the workflow is valid and satisfies the user prompt, return:

```json
{
  "valid": true,
  "errors": [],
  "warnings": []
}```
"""
main_prompt = """
Main Agent Prompt

This agent orchestrates workflow generation and validation using the following tools:

Tools:
- workflow_generation: Takes a user prompt and generates a workflow.
- validator: Takes a workflow, user prompt, enhanced prompt, and validation criteria to ensure correctness.

Process:
1. If the user input is not clear → ask the user for clarification.
2. If the user input is clear but missing required details → ask the user for more information.
3. If the user input is clear and contains all required details:
    a. Call "workflow_generation" to generate or improve a workflow.
    b. Call "validator" to check if the workflow is correct and complete.
    c. If the validator returns {"valid": true} → provide the output.
    d. If the validator returns {"valid": false} → call "workflow_generation" again,
       including both the current workflow and the validator's error messages for refinement.
       do not validator more than once. after the second attempt, return the workflow.
4. If the user input is not related to workflow generation → answer the question directly.

Output Schema:
All responses must follow this schema:

```json
{
  "message": "..." // Natural language explanation, guidance, or answer.
  "workflow": {...} // Only if a workflow is generated.
}
```
"""


def get_agents(logger):
    prompt_enhancement_agent = Agent(
        model=model,
        system_prompt=prompt_enhancement_system_prompt,
        callback_handler=get_callback_logger("prompt_enhancement", logger),
    )

    workflow_generation_agent = Agent(
        model=reasoning_model,
        tools=tools.workflow_generation_tools,
        system_prompt=workflow_generation_system_prompt,
        callback_handler=get_callback_logger("workflow_generation", logger),
    )

    validator_agent = Agent(
        model=model,
        system_prompt=validator_system_prompt,
        callback_handler=get_callback_logger("validator", logger),
    )

    @tool(
        name="workflow_generation",
        description="Generate a workflow, input is a json with user_prompt, enhanced_prompt, and criteria.",
    )
    def workflow_generation(x):
        return workflow_generation_agent(x)

    @tool(
        name="validator",
        description="Validate a workflow, input is a json with workflow, user_prompt, enhanced_prompt, and criteria.",
    )
    def validator(x):
        return validator_agent(x)

    main_agent = Agent(
        model=model,
        system_prompt=main_prompt,
        tools=[workflow_generation, validator],
        callback_handler=get_callback_logger("main", logger),
    )

    post_processing_agent = PostProcessingNode(logger)
    return {
        "prompt_enhancement": prompt_enhancement_agent,
        "main": main_agent,
        "post_processing": post_processing_agent,
    }
