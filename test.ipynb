{"cells": [{"cell_type": "code", "execution_count": 1, "id": "edff0734", "metadata": {}, "outputs": [], "source": ["demo = {'category': 'general', 'description': \"A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.\", 'id': '7dffae28-d271-4b75-acee-f738dcc26b72', 'mcp_name': 'Tavily Search 2.0', 'name': 'tavily-search', 'type': 'mcp'}"]}, {"cell_type": "code", "execution_count": 2, "id": "eaf054dd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Output schema is not a dictionary: <class 'NoneType'>\n"]}, {"data": {"text/plain": ["\"Name : tavily-search\\nDescription : A powerful web search tool that provides comprehensive, real-time results using <PERSON><PERSON>'s AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.\\nOriginalType : MCP_Tavily_Search_2.0_tavily_search\\nType : MCP\\nMCP_id : 7dffae28-d271-4b75-acee-f738dcc26b72\\nToolName : tavily-search\\nInputs :-\\nInput Name : query\\nInput Info : Search query\\nInput Type : string\\nRequired\\nHandle\\n\\nInput Name : search_depth\\nInput Info : The depth of the search. It can be 'basic' or 'advanced'\\nInput Type : string\\nHandle\\nDefault Value : basic\\n\\nInput Name : topic\\nInput Info : The category of the search. This will determine which of our agents will be used for the search\\nInput Type : string\\nHandle\\nDefault Value : general\\n\\nInput Name : days\\nInput Info : The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic\\nInput Type : number\\nHandle\\nDefault Value : 3\\n\\nInput Name : time_range\\nInput Info : The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics\\nInput Type : string\\nHandle\\n\\nInput Name : max_results\\nInput Info : The maximum number of search results to return\\nInput Type : number\\nHandle\\nDefault Value : 10\\n\\nInput Name : include_images\\nInput Info : Include a list of query-related images in the response\\nInput Type : boolean\\nHandle\\nDefault Value : False\\n\\nInput Name : include_image_descriptions\\nInput Info : Include a list of query-related images and their descriptions in the response\\nInput Type : boolean\\nHandle\\nDefault Value : False\\n\\nInput Name : include_raw_content\\nInput Info : Include the cleaned and parsed HTML content of each search result\\nInput Type : boolean\\nHandle\\nDefault Value : False\\n\\nInput Name : include_domains\\nInput Info : A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site\\nInput Type : array\\nHandle\\nList\\nDefault Value : []\\nItems : {'type': 'string'}\\n\\nInput Name : exclude_domains\\nInput Info : List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site\\nInput Type : array\\nHandle\\nList\\nDefault Value : []\\nItems : {'type': 'string'}\\n\\nOutputs :-\\nOutput Name : result\\nOutput Info : Result\\nOutput Type : Any\\n\\n\""]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from helper import mcp_context\n", "mcp_context(demo)"]}, {"cell_type": "code", "execution_count": null, "id": "58232aca", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}